/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Main navbar container - Single Row Layout */
.navbar-container {
  display: flex;
  align-items: center;
  width: 100%;
  height: 110px;
  background: #f5f4f4;
  position: relative;
  font-family: "Sora", sans-serif;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Left side - Logo and Brand only */
.navbar-left {
  display: flex;
  align-items: center;
  gap: 30px;
  padding-left: 50px;
  z-index: 2;
}

.navbar-logo {
  height: 110px;
  width: auto;
}

.navbar-brand {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.brand-title {
  font-size: 22px;
  font-weight: 800;
  color: #2c2c2c;
  letter-spacing: 2px;
  line-height: 1.1;
}

.brand-subtitle {
  font-size: 11px;
  font-weight: 600;
  color: #ff4d29;
  letter-spacing: 3px;
  margin-top: 3px;
}

/* Contact Info - Positioned above navbar-right */
.navbar-contact {
  display: flex;
  gap: 30px;
  align-items: center;
  position: absolute;
  right: 200px;
  top: 60%;
  transform: translateY(-50%);
  z-index: 10;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.contact-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  width: 20px;
  height: 20px;
  color: #ff4d29;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.contact-label {
  font-size: 11px;
  color: #ff4d29;
  font-weight: 700;
  line-height: 1;
}

.contact-value {
  font-size: 12px;
  color: #333;
  font-weight: 600;
  line-height: 1;
}

/* Right side - Navigation in Orange Section (Reduced Height) */
.navbar-right {
  display: flex;
  align-items: center;
  flex: 1;
  height: 60px;
  background: linear-gradient(135deg, #ff4d29 0%, #ff6347 100%);
  margin-left: auto;
  padding: 0 50px 0 100px;
  position: absolute;
  right: 0;
  top: 90px;
  bottom: 0;
  z-index: 999;
  clip-path: polygon(15% 0, 100% 0, 100% 100%, 0% 100%);
}

/* Navigation Menu */
.navbar-nav {
  height: 100%;
}

.nav-menu {
  display: flex;
  align-items: center;
  list-style: none;
  margin: 0;
  padding: 0;
  height: 100%;
  gap: 35px;
}

.nav-item {
  height: 100%;
  display: flex;
  align-items: center;
  position: relative;
}

.nav-item a {
  color: #fff;
  text-decoration: none;
  font-size: 15px;
  font-weight: 600;
  transition: all 0.3s ease;
  padding: 0 8px;
  text-transform: capitalize;
}

.nav-item:hover a {
  color: rgba(255, 255, 255, 0.85);
}

.nav-item.active a {
  color: #fff;
  font-weight: 700;
}

.nav-item.active::after {
  content: "";
  position: absolute;
  bottom: 12px;
  left: 50%;
  transform: translateX(-50%);
  width: 90%;
  height: 3px;
  background: #fff;
  border-radius: 2px;
}

/* Mobile Menu Toggle Button */
.mobile-menu-toggle {
  display: none;
  background: none;
  border: none;
  font-size: 24px;
  color: #ff4d29;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1001;
  box-shadow: 0 2px 8px rgba(255, 77, 41, 0.2);
}

.mobile-menu-toggle:hover {
  background-color: rgba(255, 77, 41, 0.1);
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(255, 77, 41, 0.3);
}

.mobile-menu-toggle:active {
  transform: scale(0.95);
}

/* Responsive Design */
@media (max-width: 1400px) {
  .navbar-contact {
    gap: 25px;
    right: 180px;
  }

  .contact-label {
    font-size: 10px;
  }

  .contact-value {
    font-size: 11px;
  }
}

@media (max-width: 1200px) {
  .navbar-left {
    padding-left: 30px;
    gap: 20px;
  }

  .navbar-contact {
    gap: 20px;
    right: 140px;
  }

  .navbar-right {
    padding: 0 30px 0 70px;
  }

  .nav-menu {
    gap: 25px;
  }

  .contact-label {
    font-size: 10px;
  }

  .contact-value {
    font-size: 11px;
  }
}

@media (max-width: 1024px) {
  .navbar-container {
    height: 70px;
  }

  .navbar-left {
    padding-left: 20px;
    gap: 15px;
  }

  .navbar-logo {
    height: 60px;
  }

  .navbar-contact {
    gap: 15px;
    right: 120px;
  }

  .navbar-right {
    padding: 0 20px 0 60px;
    height: 45px;
  }

  .nav-menu {
    gap: 20px;
  }

  .nav-item a {
    font-size: 14px;
  }

  .contact-icon {
    font-size: 14px;
    width: 18px;
    height: 18px;
  }

  .contact-label {
    font-size: 9px;
  }

  .contact-value {
    font-size: 10px;
  }
}

@media (max-width: 992px) {
  .navbar-container {
    height: auto;
    flex-direction: column;
    padding: 15px 0;
    align-items: stretch;
    position: relative;
  }

  .mobile-menu-toggle {
    display: block;
  }

  .navbar-left {
    padding: 0 20px;
    justify-content: center;
    gap: 15px;
    order: 1;
    margin-bottom: 15px;
  }

  .navbar-logo {
    height: 50px;
  }

  .navbar-contact {
    gap: 20px;
    position: relative;
    right: auto;
    top: auto;
    transform: none;
    order: 2;
    justify-content: center;
    margin-bottom: 15px;
    padding: 0 20px;
  }

  .navbar-right {
    width: 100%;
    margin-left: 0;
    clip-path: none;
    padding: 15px 20px;
    margin-top: 0;
    justify-content: center;
    position: relative;
    top: 0;
    bottom: auto;
    height: 50px;
    order: 3;
    display: none;
  }

  .navbar-right.mobile-menu-open {
    display: flex;
  }

  .nav-menu {
    gap: 20px;
  }

  .nav-item a {
    font-size: 14px;
  }
}

@media (max-width: 768px) {
  .navbar-container {
    height: 60px;
    flex-direction: row;
    padding: 10px 20px;
    justify-content: space-between;
    align-items: center;
  }
  .mobile-menu-toggle {
    display: block;
    position: relative;
    top: auto;
    right: auto;
    z-index: 1001;
    order: 2;
    background: rgba(255, 77, 41, 0.1);
    border-radius: 8px;
  }

  .navbar-left {
    width: auto;
    justify-content: flex-start;
    padding-left: 0;
    gap: 0;
    order: 1;
  }

  .navbar-brand {
    display: none;
  }

  .navbar-contact {
    display: none;
  }
  .navbar-logo {
    height: 45px;
  }
  .navbar-right {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.95);
    z-index: 1000;
    padding: 0;
    clip-path: none;
    order: 3;
    backdrop-filter: blur(10px);
  }

  .navbar-right.mobile-menu-open {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    animation: fadeIn 0.3s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  .nav-menu {
    width: 100%;
    max-width: 300px;
    flex-direction: column;
    gap: 30px;
    align-items: center;
    padding: 40px 20px;
  }

  .nav-item {
    width: 100%;
    justify-content: center;
    opacity: 0;
  }

  .navbar-right.mobile-menu-open .nav-item {
    animation: slideInUp 0.5s ease-out forwards;
  }

  .navbar-right.mobile-menu-open .nav-item:nth-child(1) {
    animation-delay: 0.1s;
  }
  .navbar-right.mobile-menu-open .nav-item:nth-child(2) {
    animation-delay: 0.2s;
  }
  .navbar-right.mobile-menu-open .nav-item:nth-child(3) {
    animation-delay: 0.3s;
  }
  .navbar-right.mobile-menu-open .nav-item:nth-child(4) {
    animation-delay: 0.4s;
  }
  .navbar-right.mobile-menu-open .nav-item:nth-child(5) {
    animation-delay: 0.5s;
  }

  @keyframes slideInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  .nav-item a {
    font-size: 20px;
    padding: 18px 30px;
    width: 100%;
    text-align: center;
    border-radius: 15px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: rgba(255, 77, 41, 0.9);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
    font-weight: 600;
    letter-spacing: 1px;
    color: white;
    text-decoration: none;
    display: block;
  }

  .nav-item a:hover {
    background: rgba(255, 77, 41, 1);
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 10px 30px rgba(255, 77, 41, 0.4);
    border-color: rgba(255, 255, 255, 0.5);
  }

  .nav-item.active a {
    background: rgba(255, 255, 255, 0.95);
    color: #ff4d29;
    border-color: #ff4d29;
  }
}

@media (max-width: 600px) {
  .navbar-container {
    height: 55px;
    padding: 10px 15px;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  .mobile-menu-toggle {
    position: relative;
    top: auto;
    right: auto;
    font-size: 20px;
    order: 2;
  }

  .navbar-left {
    width: auto;
    justify-content: flex-start;
    padding-left: 0;
    gap: 0;
    order: 1;
  }

  .navbar-brand {
    display: none;
  }

  .navbar-contact {
    display: none;
  }
  .navbar-logo {
    height: 40px;
  }
  .navbar-right {
    top: 0;
    height: 100vh;
    padding: 0;
    order: 3;
  }

  .nav-item a {
    font-size: 18px;
    padding: 16px 25px;
  }
}

@media (max-width: 480px) {
  .navbar-container {
    height: 50px;
    padding: 8px 12px;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  .mobile-menu-toggle {
    position: relative;
    top: auto;
    right: auto;
    font-size: 18px;
    order: 2;
  }

  .navbar-left {
    width: auto;
    justify-content: flex-start;
    padding-left: 0;
    gap: 0;
    order: 1;
  }

  .navbar-brand {
    display: none;
  }

  .navbar-contact {
    display: none;
  }

  .navbar-logo {
    height: 35px;
  }
  .navbar-right {
    top: 0;
    height: 100vh;
    padding: 0;
    order: 3;
  }

  .nav-item a {
    font-size: 18px;
    padding: 15px 25px;
  }
}
