import React, { useEffect } from "react";
import {
  <PERSON>aFacebookF,
  FaTwitter,
  Fa<PERSON>nstagram,
  FaLinkedinIn,
  FaCog,
  FaIndustry,
  FaProjectDiagram,
} from "react-icons/fa";
import { FiPhone, FiMail, FiUserCheck, FiAward, FiClock } from "react-icons/fi";
import { FaCheckCircle, FaStar } from "react-icons/fa";
import aboutWorkerImg from "./images/about_worker.jpg";
import imgCasting from "./images/service_casting.jpg";
import imgJigs from "./images/service_jigs.jpg";
import imgSPM from "./images/service_spm.jpg";
import imgFabrication from "./images/service_fabrication.jpg";
import imgPrototype from "./images/service_prototype.jpg";
import heroBgImg from "./images/hero-bg-img.jpg";
import contactHeroBg from "./images/contact-hero-bg.jpg";
import client1 from "./images/client1.png";
import client2 from "./images/client2.png";
import client3 from "./images/client3.png";
import client4 from "./images/client4.png";
import client5 from "./images/client5.png";
import client6 from "./images/client6.png";
import client7 from "./images/client7.png";
import "./Home.css";
import Navbar from "./components/Navbar";
import Footer from "./components/Footer";
import TopBar from "./components/TopBar";

const AboutWatermark = () => (
  <svg
    className="about-watermark"
    viewBox="0 0 500 120"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <text
      x="0"
      y="100"
      fontFamily="Montserrat, Arial, sans-serif"
      fontWeight="800"
      fontSize="80"
      fill="#000"
      opacity="0.04"
    >
      Our Company
    </text>
  </svg>
);

const ServicesGalleryWatermark = () => (
  <svg
    className="services-gallery-watermark"
    viewBox="0 0 700 120"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <text
      x="0"
      y="100"
      fontFamily="Montserrat, Arial, sans-serif"
      fontWeight="800"
      fontSize="90"
      fill="#000"
      opacity="0.04"
    >
      Our Services
    </text>
  </svg>
);

const Home = () => {
  // Scroll-triggered animation effect
  useEffect(() => {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: "0px 0px -50px 0px",
    };

    const observer = new IntersectionObserver(entries => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add("scroll-animate");
        }
      });
    }, observerOptions);

    // Observe all sections that should have scroll animations
    const sectionsToAnimate = document.querySelectorAll(
      ".about-section, .services-gallery-section, .contact-hero-section, .clients-section, .testimonials-section"
    );

    sectionsToAnimate.forEach(section => {
      observer.observe(section);
    });

    // Cleanup observer on component unmount
    return () => {
      sectionsToAnimate.forEach(section => {
        observer.unobserve(section);
      });
    };
  }, []);

  return (
    <div className="home-root">
      {/* Top Bar */}
      <TopBar />
      {/* Mainbar: logo/company left, contact info + nav items right */}
      <Navbar />
      {/* Hero Section */}
      <section className="hero-section">
        <div className="hero-bg-overlay"></div>
        <img src={heroBgImg} alt="Hero Background" className="hero-bg-img" />
        <div className="hero-content">
          <h1>
            Experts in <span className="highlight">Castings</span> and{" "}
            <span className="highlight">SPM</span> Solutions
          </h1>
          <p>
            Delivering High-Quality, Cost-Effective Casting & SPM Solutions with
            Precision, Professionalism, and a Commitment to Excellence
          </p>
        </div>
      </section>
      {/* About/Stats Section */}
      <section className="about-section">
        <div className="about-container">
          <div className="about-left">
            <AboutWatermark />
            <div className="about-label">About Our Company</div>
            <h2 className="about-heading">
              Driven by Experience, Defined by Precision
            </h2>
            <p className="about-desc">
              Gait Engineers is led by a professional with over two decades of
              industry expertise, specializing in castings manufacturing and SPM
              (Special Purpose Machines) consulting. We provide end-to-end
              solutions, from feasibility studies to project execution, ensuring
              quality and efficiency.
            </p>
            <ul className="about-bullets">
              <li>
                <FaCheckCircle className="about-bullet-icon" />{" "}
                <span>
                  Providing comprehensive end-to-end project execution for
                  maximum efficiency
                </span>
              </li>
              <li>
                <FaCheckCircle className="about-bullet-icon" />{" "}
                <span>
                  Ensuring precision, quality, and timely delivery in every
                  project
                </span>
              </li>
              <li>
                <FaCheckCircle className="about-bullet-icon" />{" "}
                <span>
                  Delivering high-performance die casting and SPM solutions with
                  cutting-edge technology
                </span>
              </li>
            </ul>
          </div>
          <div className="about-right">
            <div className="about-stats-row">
              <div className="about-stat">
                <div className="about-stat-outer">
                  <div className="about-stat-inner">
                    <FiUserCheck className="about-stat-icon" />
                  </div>
                </div>
                <div className="about-stat-label">20+ Years of Expertise</div>
              </div>
              <div className="about-stat">
                <div className="about-stat-outer">
                  <div className="about-stat-inner">
                    <FiAward className="about-stat-icon" />
                  </div>
                </div>
                <div className="about-stat-label">500+ Projects Delivered</div>
              </div>
              <div className="about-stat">
                <div className="about-stat-outer">
                  <div className="about-stat-inner">
                    <FiClock className="about-stat-icon" />
                  </div>
                </div>
                <div className="about-stat-label">100% On-Time Delivery</div>
              </div>
            </div>
            <div className="about-image-block">
              <img src={aboutWorkerImg} alt="Worker" className="about-image" />
            </div>
          </div>
        </div>
      </section>
      {/* Services Gallery Grid Section (the only services section) */}
      <section className="services-gallery-section">
        <div className="services-gallery-container">
          <ServicesGalleryWatermark />
          <div className="services-gallery-label">Our Services</div>
          <h2 className="services-gallery-heading">
            Engineering excellence from
            <br />
            feasibility to execution.
          </h2>
          <div className="services-gallery-underline"></div>
          <div className="services-gallery-grid">
            <div className="services-gallery-card">
              <img src={imgCasting} alt="Non-Ferrous Castings" />
              <div className="services-gallery-overlay">
                <div className="services-gallery-title">
                  Non-Ferrous Castings
                </div>
                <div className="services-gallery-desc">
                  Precision casting for aluminum, brass, and copper components
                </div>
              </div>
            </div>
            <div className="services-gallery-card">
              <img src={imgJigs} alt="Tools, Jigs & Fixtures" />
              <div className="services-gallery-overlay">
                <div className="services-gallery-title">
                  Tools, Jigs & Fixtures
                </div>
                <div className="services-gallery-desc">
                  Custom manufacturing aids for precision assembly
                </div>
              </div>
            </div>
            <div className="services-gallery-card">
              <img src={imgSPM} alt="Special Purpose Machine" />
              <div className="services-gallery-overlay">
                <div className="services-gallery-title">
                  Special Purpose Machines
                </div>
                <div className="services-gallery-desc">
                  Automated solutions for industrial processes
                </div>
              </div>
            </div>
            <div className="services-gallery-card">
              <img src={imgFabrication} alt="Fabrication, Erection" />
              <div className="services-gallery-overlay">
                <div className="services-gallery-title">
                  Fabrication & Erection
                </div>
                <div className="services-gallery-desc">
                  Complete structural fabrication and installation
                </div>
              </div>
            </div>
            <div className="services-gallery-card">
              <img src={imgPrototype} alt="Prototype & Pilot Lot Development" />
              <div className="services-gallery-overlay">
                <div className="services-gallery-title">
                  Prototype Development
                </div>
                <div className="services-gallery-desc">
                  From concept to pilot production development
                </div>
              </div>
            </div>
            <div className="services-gallery-viewmore">
              <div className="services-gallery-viewmore-glow"></div>
              <button className="services-gallery-viewmore-btn">
                View More
              </button>
            </div>
          </div>
        </div>
      </section>
      {/* Contact Hero Section */}
      <section className="contact-hero-section">
        <div className="contact-hero-bg-overlay"></div>
        <img
          src={contactHeroBg}
          alt="Contact Hero Background"
          className="contact-hero-bg-img"
        />
        <div className="contact-hero-content">
          <h1>
            <span className="highlight">Precision</span> in Every Cast,{" "}
            <span className="highlight">Excellence</span> in Every Product.
          </h1>
          <p>Partner with us for high-quality die casting solutions</p>
          <button className="contact-hero-btn">Get in Touch</button>
        </div>
      </section>

      {/* Clients Section */}
      <section className="clients-section">
        <div className="clients-container">
          <div className="clients-label">Our Clients</div>
          <h2 className="clients-heading">
            Trusted by Industry Leaders,
            <span className="clients-heading-break"> Powered by </span>
            Partnerships
          </h2>
          <div className="custom-clients-carousel">
            <div className="custom-clients-track">
              <img src={client1} alt="Client 1" className="custom-client-img" />
              <img src={client2} alt="Client 2" className="custom-client-img" />
              <img src={client3} alt="Client 3" className="custom-client-img" />
              <img src={client4} alt="Client 4" className="custom-client-img" />
              <img src={client5} alt="Client 5" className="custom-client-img" />
              <img src={client6} alt="Client 6" className="custom-client-img" />
              <img src={client7} alt="Client 7" className="custom-client-img" />
              {/* Duplicate for seamless loop */}
              <img src={client1} alt="Client 1" className="custom-client-img" />
              <img src={client2} alt="Client 2" className="custom-client-img" />
              <img src={client3} alt="Client 3" className="custom-client-img" />
              <img src={client4} alt="Client 4" className="custom-client-img" />
              <img src={client5} alt="Client 5" className="custom-client-img" />
              <img src={client6} alt="Client 6" className="custom-client-img" />
              <img src={client7} alt="Client 7" className="custom-client-img" />
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="testimonials-section">
        <div className="testimonials-container">
          <div className="testimonials-label">Testimonials</div>
          <div className="testimonials-grid">
            <div className="testimonial-card">
              <div className="testimonial-stars">
                <FaStar />
                <FaStar />
                <FaStar />
                <FaStar />
                <FaStar />
              </div>
              <p className="testimonial-quote">
                "Gait Engineers exceeded our expectations with their precise and
                timely delivery. Their expertise in SPM solutions is truly
                remarkable"
              </p>
              <div className="testimonial-author-block">
                <div className="testimonial-author">Anil Kumar</div>
                <div className="testimonial-designation">
                  Senior Project Manager
                </div>
              </div>
            </div>
            <div className="testimonial-card">
              <div className="testimonial-stars">
                <FaStar />
                <FaStar />
                <FaStar />
                <FaStar />
                <FaStar />
              </div>
              <p className="testimonial-quote">
                "Their attention to detail and commitment to quality made our
                casting project a success. Highly professional and reliable
                team."
              </p>
              <div className="testimonial-author-block">
                <div className="testimonial-author">Priya Sharma</div>
                <div className="testimonial-designation">Operations Head</div>
              </div>
            </div>
            <div className="testimonial-card">
              <div className="testimonial-stars">
                <FaStar />
                <FaStar />
                <FaStar />
                <FaStar />
                <FaStar />
              </div>
              <p className="testimonial-quote">
                "Gait Engineers exceeded our expectations with their precise and
                timely delivery. Their expertise in SPM solutions is truly
                remarkable"
              </p>
              <div className="testimonial-author-block">
                <div className="testimonial-author">Anil Kumar</div>
                <div className="testimonial-designation">
                  Senior Project Manager
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer Section */}
      <Footer />
    </div>
  );
};

export default Home;
