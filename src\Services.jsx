import React, { useEffect } from "react";
import Navbar from "./components/Navbar";
import Footer from "./components/Footer";
import TopBar from "./components/TopBar";
import { FiSearch } from "react-icons/fi";
import "./Services.css";

import ourProduct1 from "./images/our-product1.jpg";
import ourProduct2 from "./images/our-product2.jpg";
import ourProduct3 from "./images/our-product3.jpg";
import sandCast1 from "./images/Sand-cast1.jpg";
import sandCast2 from "./images/Sand-cast2.jpg";
import sandCast3 from "./images/sand-cast3.jpg";
import gravityDie1 from "./images/gravity-die1.jpg";
import gravityDie2 from "./images/gravity-die2.jpg";
import pressureDie1 from "./images/pressure-die1.jpg";

const Services = () => {
  // Scroll-triggered animation effect
  useEffect(() => {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: "0px 0px -50px 0px",
    };

    const observer = new IntersectionObserver(entries => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add("scroll-animate");
        }
      });
    }, observerOptions);

    // Observe all sections that should have scroll animations
    const sectionsToAnimate = document.querySelectorAll(
      ".services-header-flex-row, .services-section, .our-products-section, .sand-casting-section"
    );

    sectionsToAnimate.forEach(section => {
      observer.observe(section);
    });

    // Cleanup observer on component unmount
    return () => {
      sectionsToAnimate.forEach(section => {
        observer.unobserve(section);
      });
    };
  }, []);

  return (
    <>
      <TopBar />
      <Navbar />
      <div className="services-header-flex-row">
        <span className="services-title-main">Our Services</span>
        <div className="search-container">
          <FiSearch className="search-icon" />
          <input
            className="services-search"
            type="text"
            placeholder="Search our services"
          />
        </div>
      </div>
      <div className="services-section">
        <aside className="services-sidebar">
          <ul className="services-categories">
            <li className="active">
              <span className="category-title">Casting Manufacturing</span>
              <ul>
                <li className="active">
                  <span className="underlined">Prototype</span> Development
                </li>
                <li>Pilot Lot Development</li>
                <li>Low-pressure & Gravity Die Casting</li>
                <li>Shell Moulding & Sand Casting</li>
              </ul>
            </li>
            <li>Special Purpose Machines</li>
            <li>Tools, Jigs & Fixtures</li>
            <li>Fabrication, Erection & Commissioning</li>
          </ul>
        </aside>
        <main className="services-content">
          <div className="services-details">
            <h2>
              Precision Prototypes, Perfected for{" "}
              <span className="highlight">Production</span>
            </h2>
            <p>Our Capabilities in Prototype Development:</p>
            <ul>
              <li>
                <strong>Custom Molding & Tooling:</strong> Precision-engineered
                to match client specifications.
              </li>
              <li>
                <strong>Pilot Lot Development:</strong> Small-scale production
                for testing and validation.
              </li>
              <li>
                <strong>Dimensional & Strength Testing:</strong> Ensuring
                accuracy before mass production.
              </li>
              <li>
                <strong>Process Optimization:</strong> Identifying the best
                manufacturing approach for full-scale rollout.
              </li>
            </ul>
          </div>
        </main>
      </div>

      {/* Our Products Section */}
      <div className="our-products-section">
        <div className="our-products-images-row">
          <img src={ourProduct1} alt="Product 1" className="our-product-img" />
          <img src={ourProduct2} alt="Product 2" className="our-product-img" />
          <img src={ourProduct3} alt="Product 3" className="our-product-img" />
        </div>
        <div className="our-products-banner">
          <span>Our Products</span>
        </div>

        {/* Sand Casting Section */}
        <div className="sand-casting-section">
          <h3 className="sand-casting-title">Sand Casting</h3>
          <div className="sand-casting-row">
            <div className="sand-casting-item">
              <img
                src={sandCast1}
                alt="Aluminium Sand Casting"
                className="sand-casting-img"
              />
              <div className="sand-casting-label">Aluminium</div>
            </div>
            <div className="sand-casting-item">
              <img
                src={sandCast2}
                alt="Brass Sand Casting"
                className="sand-casting-img"
              />
              <div className="sand-casting-label">Brass</div>
            </div>
            <div className="sand-casting-item">
              <img
                src={sandCast3}
                alt="Lead Sand Casting"
                className="sand-casting-img"
              />
              <div className="sand-casting-label">Lead</div>
            </div>
          </div>
        </div>

        {/* Gravity Die Casting Section */}
        <div className="sand-casting-section">
          <h3 className="sand-casting-title">Gravity Die Casting</h3>
          <div className="sand-casting-row">
            <div className="sand-casting-item">
              <img
                src={gravityDie1}
                alt="Aluminium Gravity Die Casting"
                className="sand-casting-img"
              />
              <div className="sand-casting-label">Aluminium</div>
            </div>
            <div className="sand-casting-item">
              <img
                src={gravityDie2}
                alt="Brass Gravity Die Casting"
                className="sand-casting-img"
              />
              <div className="sand-casting-label">Brass</div>
            </div>
          </div>
        </div>

        {/* Pressure Die Casting Section */}
        <div className="sand-casting-section">
          <h3 className="sand-casting-title">Pressure Die Casting</h3>
          <div className="sand-casting-row">
            <div className="sand-casting-item">
              <img
                src={pressureDie1}
                alt="Aluminium Pressure Die Casting"
                className="sand-casting-img"
              />
              <div className="sand-casting-label">Aluminium</div>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </>
  );
};

export default Services;
