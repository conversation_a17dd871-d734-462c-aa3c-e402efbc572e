import React, { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { FiPhone, FiMail, FiMenu, FiX } from "react-icons/fi";
import "./Navbar.css";

const Navbar = () => {
  const location = useLocation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };return (
    <div className="navbar-container">      <div className="navbar-left">
        <img
          src="./logo.svg"
          alt="Gait Engineers Logo"
          className="navbar-logo"
        />
      </div>
        <div className="navbar-contact">
        <div className="contact-item">
          <span className="contact-icon">
            <FiPhone />
          </span>
          <div className="contact-info">
            <div className="contact-label">Call Us</div>
            <div className="contact-value">+91 94492 62225</div>
          </div>
        </div>
        <div className="contact-item">
          <span className="contact-icon">
            <FiMail />
          </span>
          <div className="contact-info">
            <div className="contact-label">Mail Us</div>
            <div className="contact-value"><EMAIL></div>
          </div>
        </div>
      </div>
      
      <button className="mobile-menu-toggle" onClick={toggleMobileMenu}>
        {isMobileMenuOpen ? <FiX /> : <FiMenu />}
      </button>
        <div className={`navbar-right ${isMobileMenuOpen ? 'mobile-menu-open' : ''}`} onClick={(e) => {
        // Close menu if clicking on the overlay background
        if (e.target === e.currentTarget) {
          setIsMobileMenuOpen(false);
        }
      }}>
        <nav className="navbar-nav">
          <ul className="nav-menu">
            <li className={location.pathname === "/" ? "nav-item active" : "nav-item"}>
              <Link to="/" onClick={() => setIsMobileMenuOpen(false)}>Home</Link>
            </li>
            <li className={location.pathname === "/about" ? "nav-item active" : "nav-item"}>
              <Link to="/about" onClick={() => setIsMobileMenuOpen(false)}>About</Link>
            </li>
            <li className={location.pathname === "/services" ? "nav-item active" : "nav-item"}>
              <Link to="/services" onClick={() => setIsMobileMenuOpen(false)}>Service</Link>
            </li>
            <li className={location.pathname === "/gallery" ? "nav-item active" : "nav-item"}>
              <Link to="/gallery" onClick={() => setIsMobileMenuOpen(false)}>Gallery</Link>
            </li>
            <li className={location.pathname === "/contact" ? "nav-item active" : "nav-item"}>
              <Link to="/contact" onClick={() => setIsMobileMenuOpen(false)}>Contact Us</Link>
            </li>
          </ul>
        </nav>
      </div>
    </div>
  );
};

export default Navbar;
