import React, { useEffect } from "react";
import Navbar from "./components/Navbar";
import Footer from "./components/Footer";
import TopBar from "./components/TopBar";
import "./ContactUs.css";
import contactUsImg from "./images/contact-us.jpg";
import { FaPhone, FaEnvelope, FaMapMarkerAlt } from "react-icons/fa";

const ContactUs = () => {
  // Scroll-triggered animation effect
  useEffect(() => {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: "0px 0px -50px 0px",
    };

    const observer = new IntersectionObserver(entries => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add("scroll-animate");
        }
      });
    }, observerOptions);

    // Observe all sections that should have scroll animations
    const sectionsToAnimate = document.querySelectorAll(
      ".contact-section, .contact-content, .contact-image-container"
    );

    sectionsToAnimate.forEach(section => {
      observer.observe(section);
    });

    // Cleanup observer on component unmount
    return () => {
      sectionsToAnimate.forEach(section => {
        observer.unobserve(section);
      });
    };
  }, []);

  return (
    <div className="contact-us-page">
      <TopBar />
      <Navbar />
      <div className="contact-section">
        <div className="contact-content">
          <p className="contact-label">Contact Us</p>
          <h1 className="contact-heading">
            Partner with{" "}
            <span className="highlight italic">Gait Engineering</span> for
            Excellence in Die Casting!
          </h1>
          <p className="contact-description">
            Get in touch with us today to discuss your project requirements.
          </p>

          <div className="contact-info">
            <div className="info-item">
              <FaPhone className="info-icon" />
              <span>+91-94492-62225</span>
            </div>
            <div className="info-item">
              <FaEnvelope className="info-icon" />
              <span><EMAIL></span>
            </div>
            <div className="info-item">
              <FaMapMarkerAlt className="info-icon" />
              <span>
                Address: #26A, Muneeswara Layout, 5th Cross, Near SEA College
                Arch, K.R. Puram, Bangalore - 560036, Karnataka, India
              </span>
            </div>
          </div>

          <button className="get-in-touch-button">Get In Touch</button>
        </div>
        <div className="contact-image-container">
          <img src={contactUsImg} alt="Contact Us" className="contact-image" />
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default ContactUs;
