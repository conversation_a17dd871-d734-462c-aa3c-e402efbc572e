/* Footer Styles */
.footer-section {
  background-color: #515151;
  color: #fff;
  padding: 60px 0 20px 0;
  font-family: "Sora", sans-serif;
}

.footer-container {
  max-width: 1300px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  padding: 0 32px;
}

.footer-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 30px;
  /* border-bottom: 1px solid #bdbdbd; */
  margin-bottom: 24px;
}

.footer-logo-block {
  display: flex;
  align-items: center;
  gap: 15px;
}

.footer-logo-img {
  height: 120px;
  width: auto;
  max-width: 200px;
  object-fit: contain;
  filter: none !important;
}

.footer-nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  gap: 48px;
}

.footer-nav li {
  font-size: 1.08rem;
  font-weight: 600;
  cursor: pointer;
  transition: color 0.2s;
  color: #fff;
}

.footer-nav li:hover {
  color: #ff4d29;
}

.footer-middle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 0;
  border-bottom: 1px solid #898787;
  margin-bottom: 18px;
}

.footer-contact-info {
  display: flex;
  gap: 48px;
  font-size: 1.08rem;
  font-weight: 600;
  color: #fff;
}

.footer-contact-info span {
  display: flex;
  align-items: center;
  gap: 8px;
}

.footer-social-icons {
  display: flex;
  align-items: center;
}

.footer-social-icons a {
  color: #fff;
  font-size: 22px;
  margin-left: 28px;
  transition: color 0.2s;
}

.footer-social-icons a:first-child {
  margin-left: 0;
}

.footer-social-icons a:hover {
  color: #ff4d29;
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 1rem;
  color: #ccc;
  padding-top: 8px;
}

/* Tablet Large - 1200px and below */
@media (max-width: 1200px) {
  .footer-container {
    max-width: 100%;
    padding: 0 24px;
  }

  .footer-nav ul {
    gap: 32px;
  }

  .footer-contact-info {
    gap: 32px;
  }
}

/* Tablet Medium - 992px and below */
@media (max-width: 992px) {
  .footer-top,
  .footer-middle {
    flex-direction: column;
    gap: 24px;
    text-align: left;
    align-items: flex-start;
  }

  .footer-nav ul {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-start;
    gap: 24px;
  }

  .footer-contact-info {
    gap: 24px;
    justify-content: flex-start;
  }

  .footer-social-icons {
    margin-top: 0;
    justify-content: flex-start;
  }

  .footer-container {
    padding: 0 20px;
  }

  .footer-logo-img {
    height: 100px;
  }
}

/* Tablet Small - 768px and below */
@media (max-width: 768px) {
  .footer-section {
    padding: 40px 0 20px 0;
  }

  .footer-container {
    padding: 0 16px;
  }

  .footer-top {
    padding-bottom: 24px;
    margin-bottom: 20px;
  }

  .footer-nav ul {
    gap: 20px;
    font-size: 0.95rem;
  }

  .footer-nav li {
    font-size: 1rem;
  }

  .footer-middle {
    padding: 20px 0;
    margin-bottom: 16px;
  }

  .footer-contact-info {
    gap: 20px;
    font-size: 1rem;
    flex-direction: column;
  }

  .footer-social-icons a {
    font-size: 20px;
    margin-left: 20px;
  }

  .footer-bottom {
    flex-direction: column;
    gap: 12px;
    font-size: 0.9rem;
    padding-top: 12px;
    align-items: flex-start;
    text-align: left;
  }

  .footer-logo-img {
    height: 80px;
  }
}

/* Mobile Large - 576px and below */
@media (max-width: 576px) {
  .footer-section {
    padding: 32px 0 16px 0;
  }

  .footer-container {
    padding: 0 12px;
  }

  .footer-top {
    padding-bottom: 20px;
    margin-bottom: 16px;
    align-items: flex-start;
  }

  .footer-nav ul {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .footer-nav li {
    font-size: 0.95rem;
  }

  .footer-middle {
    padding: 16px 0;
    margin-bottom: 12px;
    gap: 20px;
    align-items: flex-start;
  }

  .footer-contact-info {
    gap: 16px;
    font-size: 0.9rem;
  }

  .footer-social-icons a {
    font-size: 18px;
    margin-left: 16px;
  }

  .footer-bottom {
    gap: 8px;
    font-size: 0.85rem;
    align-items: flex-start;
    text-align: left;
  }

  .footer-logo-img {
    height: 70px;
  }
}

/* Mobile Small - 480px and below */
@media (max-width: 480px) {
  .footer-section {
    padding: 24px 0 12px 0;
  }

  .footer-container {
    padding: 0 8px;
  }

  .footer-nav ul {
    gap: 12px;
  }

  .footer-nav li {
    font-size: 0.9rem;
    font-weight: 500;
  }

  .footer-contact-info {
    font-size: 0.85rem;
    gap: 12px;
  }

  .footer-social-icons a {
    font-size: 16px;
    margin-left: 12px;
  }

  .footer-bottom {
    font-size: 0.8rem;
    align-items: flex-start;
    text-align: left;
  }

  .footer-logo-img {
    height: 60px;
    max-width: 150px;
  }
}

/* Mobile Extra Small - 360px and below */
@media (max-width: 360px) {
  .footer-section {
    padding: 20px 0 10px 0;
  }

  .footer-container {
    padding: 0 6px;
  }

  .footer-nav li {
    font-size: 0.85rem;
  }

  .footer-contact-info {
    font-size: 0.8rem;
  }

  .footer-social-icons a {
    font-size: 14px;
    margin-left: 10px;
  }

  .footer-bottom {
    font-size: 0.75rem;
    align-items: flex-start;
    text-align: left;
  }

  .footer-logo-img {
    height: 50px;
    max-width: 120px;
  }
}
