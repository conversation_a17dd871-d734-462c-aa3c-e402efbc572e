// App.jsx

import React from "react";
import { BrowserRouter as Router, Route, Routes } from "react-router-dom";
import Home from "./Home";
import AboutUs from "./AboutUs";
import Gallery from "./Gallery";
import ContactUs from "./ContactUs";
import Services from "./Services";
import "./App.css";

const App = () => {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<Home />} />
        <Route path="/about" element={<AboutUs />} />
        <Route path="/gallery" element={<Gallery />} />
        <Route path="/contact" element={<ContactUs />} />
        <Route path="/services" element={<Services />} />
        {/* Add other routes here as needed */}
      </Routes>
    </Router>
  );
};

export default App;

// Generated with Dualite Figma Plugin
