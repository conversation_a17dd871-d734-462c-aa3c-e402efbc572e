@import url("https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700;800&family=Sora:wght@400;600;700;800&display=swap");

/* Smooth scrolling for the entire page */
html {
  scroll-behavior: smooth;
}

body,
.home-root {
  overflow-x: hidden;
  width: 100%;
}

.home-root {
  font-family: "Montserrat", "Sora", Arial, sans-serif;
  background: #fff;
  min-height: 100vh;
  position: relative;
  /* Enhanced performance for animations */
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Global fade-in animation for all sections */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-40px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(40px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Floating animation for special elements */
@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Rotating animation for icons */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Smooth reveal animation */
@keyframes reveal {
  from {
    opacity: 0;
    clip-path: inset(100% 0 0 0);
  }
  to {
    opacity: 1;
    clip-path: inset(0 0 0 0);
  }
}

/* Performance optimizations for animations */
*,
*::before,
*::after {
  backface-visibility: hidden;
  transform-style: preserve-3d;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

.top-bar {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
  color: #fff;
  font-size: 15px;
  padding: 8px 40px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
}

.top-bar::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.top-bar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}

.top-bar-welcome {
  display: block;
  font-weight: 500;
  letter-spacing: 0.5px;
  animation: slideInLeft 0.8s ease-out;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.brand-highlight {
  color: #ff4d29;
  font-weight: 700;
  text-shadow: 0 0 10px rgba(255, 77, 41, 0.3);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%,
  100% {
    text-shadow: 0 0 10px rgba(255, 77, 41, 0.3);
  }
  50% {
    text-shadow: 0 0 20px rgba(255, 77, 41, 0.6),
      0 0 30px rgba(255, 77, 41, 0.4);
  }
}

.top-bar-icons {
  display: flex;
  gap: 15px;
  animation: slideInRight 0.8s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  color: #ecf0f1;
  font-size: 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.social-icon::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    45deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.social-icon:hover::before {
  left: 100%;
}

.social-icon:hover {
  color: #fff;
  background: #ff4d29;
  transform: translateY(-2px) scale(1.1);
  box-shadow: 0 8px 25px rgba(255, 77, 41, 0.4);
}

.social-icon:nth-child(1):hover {
  background: #1877f2;
  box-shadow: 0 8px 25px rgba(24, 119, 242, 0.4);
}

.social-icon:nth-child(2):hover {
  background: #1da1f2;
  box-shadow: 0 8px 25px rgba(29, 161, 242, 0.4);
}

.social-icon:nth-child(3):hover {
  background: #e4405f;
  box-shadow: 0 8px 25px rgba(228, 64, 95, 0.4);
}

.social-icon:nth-child(4):hover {
  background: #0077b5;
  box-shadow: 0 8px 25px rgba(0, 119, 181, 0.4);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 18px 40px 0 40px;
  background: #fff;
}
.logo-section {
  display: flex;
  align-items: center;
}
.logo-img {
  height: 60px;
  margin-right: 18px;
}
.logo-text .brand-title {
  font-family: "Montserrat", sans-serif;
  font-weight: 800;
  font-size: 20px;
  color: #ff270a;
  letter-spacing: 1px;
}
.logo-text .brand-sub {
  font-family: "Montserrat", sans-serif;
  font-weight: 400;
  font-size: 12px;
  color: #535353;
  letter-spacing: 1px;
}
image.pngontact {
  display: flex;
  gap: 32px;
  align-items: center;
}
.contact-block {
  display: flex;
  align-items: center;
  gap: 8px;
}
.contact-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22px;
  width: 28px;
  height: 28px;
}
.contact-icon.call {
  color: #ff270a;
}
.contact-icon.mail {
  color: #ff270a;
}
.contact-label {
  font-size: 13px;
  color: #535353;
  font-weight: 700;
}
.contact-value {
  font-size: 14px;
  color: #000;
  font-weight: 500;
}

/* Remove conflicting navbar styles - these should be handled by Navbar.css */

/* Ensure navbar container is visible and positioned correctly */
.navbar-container {
  position: relative;
  z-index: 100;
  width: 100%;
  background: #f5f4f4;
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.hero-section {
  position: relative;
  min-height: 420px;
  height: 83vh;
  display: flex;
  align-items: stretch;
  justify-content: flex-start;
  overflow: hidden;
  /* Hero animation */
  animation: fadeInUp 1.2s ease-out;
}

.hero-bg-img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 0;
  /* Subtle zoom effect */
  animation: heroZoom 20s ease-in-out infinite alternate;
  transform: scale(1.05);
}

@keyframes heroZoom {
  0% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1.1);
  }
}

.hero-bg-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    rgba(0, 0, 0, 0.8) 0%,
    rgba(0, 0, 0, 0.4) 50%,
    rgba(0, 0, 0, 0.1) 100%
  );
  z-index: 1;
  /* Subtle overlay animation */
  animation: overlayFade 1.5s ease-out;
}

@keyframes overlayFade {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.hero-content {
  position: relative;
  z-index: 2;
  color: #fff;
  padding: 60px 0 0 80px;
  max-width: 600px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
  /* Staggered animation for content */
  animation: fadeInLeft 1.5s ease-out 0.3s both;
}

.hero-content h1 {
  font-family: "Montserrat", sans-serif;
  font-size: 3.2rem;
  font-weight: 800;
  margin-bottom: 18px;
  line-height: 1.1;
  /* Text animation */
  animation: fadeInUp 1.2s ease-out 0.6s both;
}

.hero-content .highlight {
  color: #ff270a;
  /* Subtle text glow effect */
  text-shadow: 0 0 10px rgba(255, 39, 10, 0.3);
  animation: textGlow 3s ease-in-out infinite alternate;
}

@keyframes textGlow {
  from {
    text-shadow: 0 0 10px rgba(255, 39, 10, 0.3);
  }
  to {
    text-shadow: 0 0 20px rgba(255, 39, 10, 0.5),
      0 0 30px rgba(255, 39, 10, 0.2);
  }
}

.hero-content p {
  font-family: "Sora", sans-serif;
  font-size: 1.3rem;
  font-weight: 400;
  color: #fff;
  margin: 0;
  line-height: 1.5;
  /* Paragraph animation */
  animation: fadeInUp 1.2s ease-out 0.9s both;
}

/* ===============================================
   COMPREHENSIVE MOBILE RESPONSIVE STYLES
   =============================================== */

/* Hero Section Responsive Design */
@media (max-width: 1200px) {
  .hero-content {
    padding: 40px 0 0 60px;
    max-width: 550px;
  }

  .hero-content h1 {
    font-size: 2.6rem;
  }

  .hero-content p {
    font-size: 1.1rem;
  }
}

@media (max-width: 992px) {
  .hero-section {
    height: 70vh;
    min-height: 400px;
  }

  .hero-content {
    padding: 30px 0 0 40px;
    max-width: 500px;
  }

  .hero-content h1 {
    font-size: 2.2rem;
    margin-bottom: 16px;
  }

  .hero-content p {
    font-size: 1rem;
  }
}

@media (max-width: 768px) {
  .hero-section {
    height: 60vh;
    min-height: 350px;
  }

  .hero-content {
    padding: 20px 0 0 20px;
    max-width: 90%;
  }

  .hero-content h1 {
    font-size: 1.8rem;
    margin-bottom: 14px;
    line-height: 1.2;
  }

  .hero-content p {
    font-size: 0.95rem;
    line-height: 1.4;
  }
}

@media (max-width: 600px) {
  .hero-section {
    height: 50vh;
    min-height: 300px;
  }

  .hero-content {
    padding: 15px 0 0 15px;
    max-width: 85%;
  }

  .hero-content h1 {
    font-size: 1.6rem;
    margin-bottom: 12px;
  }

  .hero-content p {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .hero-section {
    height: 45vh;
    min-height: 280px;
  }

  .hero-content {
    padding: 10px 0 0 12px;
    max-width: 80%;
  }

  .hero-content h1 {
    font-size: 1.4rem;
    margin-bottom: 10px;
  }

  .hero-content p {
    font-size: 0.85rem;
  }
}

/* About Section Responsive Design */
@media (max-width: 1200px) {
  .about-container {
    gap: 40px;
    padding: 0 30px;
  }
}

@media (max-width: 992px) {
  .about-section {
    padding: 40px 0 24px 0;
  }
  .about-container {
    flex-direction: column;
    gap: 20px;
    padding: 0 12px;
    align-items: center;
    text-align: center;
  }
}

@media (max-width: 768px) {
  .about-section {
    padding: 18px 0 8px 0;
  }
  .about-container {
    padding: 0 4vw;
    gap: 10px;
  }
}

@media (max-width: 600px) {
  .about-label {
    font-size: 0.95rem;
    margin-bottom: 8px;
  }
  .about-heading {
    font-size: 1.1rem !important;
  }
  .about-desc {
    font-size: 0.8rem !important;
  }
  .about-bullets li {
    font-size: 0.75rem !important;
    display: flex;
    align-items: flex-start;
    gap: 10px;
  }
}

@media (max-width: 480px) {
  .about-section {
    padding: 6px 0 4px 0;
  }
  .about-container {
    padding: 0 2vw;
    gap: 6px;
  }
  .about-label {
    font-size: 0.85rem;
    margin-bottom: 4px;
  }
  .about-heading {
    font-size: 0.95rem !important;
  }
  .about-desc {
    font-size: 0.7rem !important;
  }
  .about-bullets li {
    font-size: 0.65rem !important;
    display: flex;
    align-items: flex-start;
    gap: 8px;
  }
}

@media (max-width: 375px) {
  .top-bar {
    padding: 4px 10px;
    height: 32px;
  }

  .top-bar-icons {
    gap: 8px;
  }

  .social-icon {
    width: 24px;
    height: 24px;
    font-size: 11px;
  }
}

/* Services Gallery Section Responsive Design */
@media (max-width: 1200px) {
  .services-gallery-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .services-gallery-heading {
    font-size: 32px;
    line-height: 1.3;
  }

  .services-gallery-card {
    height: 280px;
  }
}

@media (max-width: 992px) {
  .services-gallery-section {
    padding: 60px 20px;
  }

  .services-gallery-container {
    max-width: 100%;
  }

  .services-gallery-heading {
    font-size: 28px;
    margin-bottom: 30px;
  }

  .services-gallery-grid {
    gap: 15px;
  }

  .services-gallery-card {
    height: 250px;
  }

  .services-gallery-title {
    font-size: 18px;
  }

  .services-gallery-desc {
    font-size: 14px;
  }
}

@media (max-width: 768px) {
  .services-gallery-section {
    padding: 50px 15px;
  }

  .services-gallery-heading {
    font-size: 24px;
    text-align: center;
  }

  .services-gallery-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .services-gallery-card {
    height: 220px;
  }

  .services-gallery-overlay {
    padding: 20px;
  }

  .services-gallery-viewmore {
    height: 220px;
  }

  .services-gallery-viewmore-btn {
    padding: 12px 24px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .services-gallery-section {
    padding: 40px 10px;
  }

  .services-gallery-label {
    font-size: 14px;
    margin-bottom: 10px;
  }

  .services-gallery-heading {
    font-size: 22px;
    margin-bottom: 25px;
  }

  .services-gallery-card {
    height: 200px;
  }

  .services-gallery-title {
    font-size: 16px;
    margin-bottom: 5px;
  }

  .services-gallery-desc {
    font-size: 13px;
  }

  .services-gallery-viewmore {
    height: 200px;
  }

  .services-gallery-viewmore-btn {
    padding: 10px 20px;
    font-size: 13px;
  }
}

@media (max-width: 375px) {
  .services-gallery-section {
    padding: 30px 8px;
  }

  .services-gallery-heading {
    font-size: 20px;
  }

  .services-gallery-card {
    height: 180px;
  }

  .services-gallery-overlay {
    padding: 15px;
  }
}

/* Contact Hero Section Responsive Design */
@media (max-width: 1200px) {
  .contact-hero-content h1 {
    font-size: 2.4rem;
  }

  .contact-hero-content p {
    font-size: 1rem;
  }
}

@media (max-width: 992px) {
  .contact-hero-section {
    height: 50vh;
    min-height: 350px;
  }

  .contact-hero-content h1 {
    font-size: 2.2rem;
  }

  .contact-hero-content p {
    font-size: 0.95rem;
  }

  .contact-hero-btn {
    font-size: 1.1rem;
    padding: 16px 38px;
  }
}

@media (max-width: 768px) {
  .contact-hero-section {
    height: 45vh;
    min-height: 300px;
  }

  .contact-hero-content {
    padding: 15px;
  }

  .contact-hero-content h1 {
    font-size: 1.6rem;
    margin-bottom: 12px;
  }

  .contact-hero-content p {
    font-size: 0.85rem;
    margin-bottom: 20px;
  }

  .contact-hero-btn {
    font-size: 0.9rem;
    padding: 12px 28px;
  }
}

@media (max-width: 600px) {
  .contact-hero-content h1 {
    font-size: 1.4rem;
  }

  .contact-hero-content p {
    font-size: 0.8rem;
  }

  .contact-hero-btn {
    font-size: 0.85rem;
    padding: 10px 24px;
  }
}

@media (max-width: 480px) {
  .contact-hero-section {
    min-height: 300px;
    padding: 40px 10px;
  }

  .contact-hero-content h1 {
    font-size: 1.25rem;
    margin-bottom: 10px;
  }

  .contact-hero-content p {
    font-size: 0.75rem;
    margin-bottom: 18px;
  }

  .contact-hero-btn {
    padding: 10px 20px;
    font-size: 0.8rem;
  }
}

/* Clients Section Responsive Text Handling */
.clients-heading {
  font-size: clamp(1rem, 4vw, 2rem);
  line-height: 1.2;
  text-align: center;
  margin-bottom: clamp(15px, 2vw, 30px);
  max-width: min(90%, 800px);
  margin-left: auto;
  margin-right: auto;
  word-wrap: break-word;
  hyphens: auto;
  padding: 0 10px;
}

.clients-heading-break {
  display: inline-block;
}

@media (max-width: 1200px) {
  .clients-heading {
    font-size: clamp(0.9rem, 3.5vw, 1.8rem);
  }
}

@media (max-width: 992px) {
  .clients-heading {
    font-size: clamp(0.85rem, 3vw, 1.6rem);
    line-height: 1.3;
  }
}

@media (max-width: 768px) {
  .clients-heading {
    font-size: clamp(0.8rem, 2.8vw, 1.4rem);
    padding: 0 15px;
  }

  .clients-heading-break {
    display: block;
    margin: 3px 0;
  }
}

@media (max-width: 600px) {
  .clients-heading {
    font-size: clamp(0.75rem, 2.5vw, 1.2rem);
    line-height: 1.4;
  }
}

@media (max-width: 480px) {
  .clients-heading {
    font-size: clamp(0.7rem, 2.2vw, 1rem);
    margin-bottom: 15px;
  }

  .clients-heading-break {
    margin: 2px 0;
  }
}

@media (max-width: 375px) {
  .clients-heading {
    font-size: clamp(0.65rem, 2vw, 0.9rem);
    padding: 0 8px;
  }
}

/* Testimonials Section Responsive Design */
@media (max-width: 1200px) {
  .testimonials-section {
    padding: 60px 20px;
  }

  .testimonials-grid {
    gap: 50px;
  }
}

@media (max-width: 992px) {
  .testimonials-section {
    padding: 60px 20px;
  }

  .testimonials-container {
    max-width: 90%;
  }

  .testimonials-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .testimonial-card {
    padding: 25px;
  }

  .testimonial-quote {
    font-size: 15px;
    margin: 15px 0;
  }

  .testimonial-author {
    font-size: 16px;
  }

  .testimonial-designation {
    font-size: 13px;
  }
}

@media (max-width: 768px) {
  .testimonials-section {
    padding: 50px 15px;
  }

  .testimonials-container {
    max-width: 100%;
  }

  .testimonials-grid {
    grid-template-columns: 1fr;
    gap: 25px;
  }

  .testimonials-label {
    text-align: center;
    font-size: 0.85rem;
    margin-bottom: 25px;
  }

  .testimonial-card {
    padding: 20px;
  }

  .testimonial-stars {
    font-size: 18px;
  }

  .testimonial-quote {
    font-size: 0.9rem;
    line-height: 1.6;
  }
}

@media (max-width: 600px) {
  .testimonial-card {
    padding: 25px 20px 0 20px;
    max-width: 400px;
  }

  .testimonial-quote {
    font-size: 0.85rem;
    margin-bottom: 45px;
  }

  .testimonial-author-block {
    width: 200px;
    padding: 10px 20px 6px 20px;
  }

  .testimonial-author {
    font-size: 0.95rem;
  }

  .testimonial-designation {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .testimonials-section {
    padding: 40px 10px;
  }

  .testimonials-label {
    font-size: 0.75rem;
    margin-bottom: 20px;
  }

  .testimonial-card {
    padding: 18px;
  }

  .testimonial-stars {
    font-size: 16px;
  }

  .testimonial-quote {
    font-size: 0.8rem;
    margin: 12px 0;
  }

  .testimonial-author {
    font-size: 0.85rem;
  }

  .testimonial-designation {
    font-size: 0.7rem;
  }
}

/* Footer Responsive Design - Moved to Footer.css for better organization */

/* Top Bar Responsive Design */
@media (max-width: 900px) {
  .top-bar {
    padding: 6px 20px;
  }

  .top-bar-content {
    gap: 15px;
  }

  .top-bar-welcome {
    font-size: 14px;
  }

  .social-icon {
    width: 30px;
    height: 30px;
    font-size: 14px;
  }
}

@media (max-width: 768px) {
  .top-bar {
    padding: 6px 15px;
  }

  .top-bar-welcome {
    display: none;
  }

  .top-bar-content {
    width: 100%;
    justify-content: flex-end;
  }

  .top-bar-icons {
    gap: 12px;
    margin: 0;
  }

  .social-icon {
    width: 28px;
    height: 28px;
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .top-bar {
    padding: 4px 12px;
    justify-content: flex-end;
    height: 35px;
  }

  .top-bar-welcome {
    display: none;
  }

  .top-bar-icons {
    display: flex;
    gap: 10px;
  }

  .top-bar-icons a {
    font-size: 12px;
    margin-left: 0;
  }

  .social-icon {
    width: 26px;
    height: 26px;
  }
}

@media (max-width: 375px) {
  .top-bar {
    padding: 4px 10px;
    height: 32px;
  }

  .top-bar-icons {
    gap: 8px;
  }

  .social-icon {
    width: 24px;
    height: 24px;
    font-size: 11px;
  }
}

/* Home page styles go here */

/* Home page styles */
.home-1-40196 {
  width: 100%;
  height: 100%;
  top: 0%;
  left: 0%;
  opacity: 1;
  z-index: 0;
  transform: rotate(0deg);
}

.frame-34-407111 {
  width: 100%;
  height: 100%;
  top: 0%;
  left: 0%;
  opacity: 1;
  z-index: 0;
  transform: rotate(0deg);
}

.welcome-to-gait-407114 {
  width: 100%;
  height: 100%;
  top: 0%;
  left: 0%;
  opacity: 1;
  z-index: 0;
  transform: rotate(0deg);
  text-align: left;
  line-height: 0px;
}

.welcome-to-gait-407114-0,
.welcome-to-gait-407114-1 {
  width: 100%;
  height: 100%;
  top: 0%;
  left: 0%;
  opacity: 1;
  z-index: 0;
  transform: rotate(0deg);
  text-align: left;
  line-height: 0px;
}

/* Contact section styles */
.frame-36-412261 {
  width: 100%;
  height: 100%;
  top: 0%;
  left: 0%;
  opacity: 1;
  z-index: 0;
  transform: rotate(0deg);
}

.phone-412249 {
  width: 100%;
  height: 100%;
  top: 0%;
  left: 0%;
  opacity: 1;
  z-index: 0;
  transform: rotate(0deg);
}

.call-us-412255 {
  width: 100%;
  height: 100%;
  top: 0%;
  left: 0%;
  opacity: 1;
  z-index: 0;
  transform: rotate(0deg);
  text-align: left;
  line-height: 0px;
}

/* About section styles */
.about-our-compa-429127 {
  width: 100%;
  height: 100%;
  top: 0%;
  left: 0%;
  opacity: 1;
  z-index: 0;
  transform: rotate(0deg);
  text-align: left;
  line-height: 0px;
}

.about-our-compa-412360 {
  width: 100%;
  height: 100%;
  top: 0%;
  left: 0%;
  opacity: 1;
  z-index: 0;
  transform: rotate(0deg);
  text-align: left;
  line-height: 0px;
}

/* Services section styles */
.our-services-429126 {
  width: 100%;
  height: 100%;
  top: 0%;
  left: 0%;
  opacity: 1;
  z-index: 0;
  transform: rotate(0deg);
  text-align: left;
  line-height: 0px;
}

.our-services-412386 {
  width: 100%;
  height: 100%;
  top: 0%;
  left: 0%;
  opacity: 1;
  z-index: 0;
  transform: rotate(0deg);
  text-align: left;
  line-height: 0px;
}

/* Testimonials section styles */
.testimonials-section {
  padding: 80px 0;
  background: #fafafa;
  text-align: center;
}

/* Scroll-triggered animation for testimonials section */
.testimonials-section.scroll-animate {
  opacity: 0;
  transform: translateY(50px);
  animation: fadeInUp 1.2s ease-out 0.3s forwards;
}

.testimonials-label {
  color: #ed4d29;
  font-family: "Montserrat", sans-serif;
  font-size: 1.5rem;
  font-weight: 800;
  margin-bottom: 10px;
  letter-spacing: 1px;
  text-align: center;
  position: relative;
  display: inline-block;
}

.testimonials-label::after {
  content: "";
  display: block;
  margin: 8px auto 0 auto;
  width: 80px;
  height: 4px;
  background: #ed4d29;
  border-radius: 2px;
}

/* Scroll-triggered animations for testimonials content */
.testimonials-section.scroll-animate .testimonials-label {
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.8s ease-out 0.6s forwards;
}

.testimonials-section.scroll-animate .testimonials-label::after {
  transform: scaleX(0);
  animation: slideInScale 0.8s ease-out 1s forwards;
}

.testimonials-container {
  max-width: 1200px;
  margin: 0 auto;
}

.testimonials-grid {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: stretch;
  gap: 65px;
  margin-top: 40px;
}

.testimonial-card {
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.07);
  padding: 36px 32px 0 32px;
  position: relative;
  min-width: 340px;
  max-width: 370px;
  flex: 1 1 340px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 0;
  text-align: left;
  overflow: visible;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Scroll-triggered animations for testimonial cards */
.testimonials-section.scroll-animate .testimonial-card {
  opacity: 0;
  transform: translateY(40px) scale(0.95);
  animation: cardFadeIn 0.8s ease-out forwards;
}

/* Staggered animation for testimonial cards */
.testimonials-section.scroll-animate .testimonial-card:nth-child(1) {
  animation-delay: 1s;
}
.testimonials-section.scroll-animate .testimonial-card:nth-child(2) {
  animation-delay: 1.2s;
}
.testimonials-section.scroll-animate .testimonial-card:nth-child(3) {
  animation-delay: 1.4s;
}

.testimonial-card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
}

.testimonial-stars {
  color: #ffb23f;
  font-size: 2rem;
  margin-bottom: 18px;
  display: flex;
  gap: 6px;
  /* Stars animation */
  opacity: 0;
  animation: fadeInUp 0.6s ease-out forwards;
}

.testimonial-card:nth-child(1) .testimonial-stars {
  animation-delay: 1.3s;
}
.testimonial-card:nth-child(2) .testimonial-stars {
  animation-delay: 1.5s;
}
.testimonial-card:nth-child(3) .testimonial-stars {
  animation-delay: 1.7s;
}

.testimonial-stars svg {
  margin-right: 0;
  transition: transform 0.2s ease;
}

.testimonial-stars:hover svg {
  transform: scale(1.1) rotate(5deg);
}

.testimonial-quote {
  font-family: "Sora", sans-serif;
  font-size: 1.08rem;
  color: #222;
  line-height: 1.7;
  margin-bottom: 60px;
  font-weight: 500;
  /* Quote animation */
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.6s ease-out forwards;
}

.testimonial-card:nth-child(1) .testimonial-quote {
  animation-delay: 1.5s;
}
.testimonial-card:nth-child(2) .testimonial-quote {
  animation-delay: 1.7s;
}
.testimonial-card:nth-child(3) .testimonial-quote {
  animation-delay: 1.9s;
}

.testimonial-author-block {
  background: #515151;
  color: #fff;
  padding: 14px 32px 10px 32px;
  border-radius: 4px;
  position: absolute;
  left: 55%;
  bottom: -28px;
  width: 260px;
  min-width: 200px;
  max-width: 90%;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.13);
  transform: translateX(-20%);
  z-index: 2;
  /* Author block animation */
  opacity: 0;
  animation: slideInFromBottom 0.6s ease-out forwards;
  transition: all 0.3s ease;
}

.testimonial-card:nth-child(1) .testimonial-author-block {
  animation-delay: 1.7s;
}
.testimonial-card:nth-child(2) .testimonial-author-block {
  animation-delay: 1.9s;
}
.testimonial-card:nth-child(3) .testimonial-author-block {
  animation-delay: 2.1s;
}

@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateX(-20%) translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateX(-20%) translateY(0);
  }
}

.testimonial-card:hover .testimonial-author-block {
  transform: translateX(-20%) translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.testimonial-author {
  font-family: "Montserrat", sans-serif;
  font-size: 1.15rem;
  font-weight: 800;
  color: #fff;
  margin-bottom: 2px;
}

.testimonial-designation {
  font-family: "Sora", sans-serif;
  font-size: 0.98rem;
  font-weight: 400;
  color: #e0e0e0;
}

@media (max-width: 1100px) {
  .testimonials-section {
    padding: 60px 20px;
  }

  .testimonials-grid {
    flex-direction: column;
    align-items: center;
    gap: 35px;
  }

  .testimonial-card {
    min-width: auto;
    max-width: 500px;
    width: 100%;
  }

  .testimonial-author-block {
    left: 50%;
    transform: translateX(-50%);
    width: 240px;
  }
}

@media (max-width: 700px) {
  .testimonials-container {
    max-width: 100%;
    padding: 0 15px;
  }

  .testimonial-card {
    padding: 24px 20px 0 20px;
    max-width: 450px;
  }

  .testimonial-author-block {
    padding: 12px 20px 8px 20px;
    width: 220px;
  }
}

/* Common utility classes */
.pos-abs {
  position: absolute;
}

.fill-parent {
  width: 100%;
  height: 100%;
}

.bg-contain {
  background-size: contain;
}

.bg-cover {
  background-size: cover;
}

.bg-auto {
  background-size: auto;
}

.bg-crop {
  background-size: cover;
  background-position: center;
}

.bg-no-repeat {
  background-repeat: no-repeat;
}

.pos-init {
  position: relative;
  width: 100%;
  height: 100%;
}

.image-div {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* Home page styles */
.parent-div {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow-x: hidden;
  background-color: #ffffff;
}

.mainbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 24px 40px 0 40px;
  background: #fff;
  min-height: 90px;
}
.mainbar-left {
  display: flex;
  align-items: center;
  gap: 18px;
}
.mainbar-logo-img {
  height: 64px;
  width: auto;
  display: block;
}
.mainbar-logo-text {
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.mainbar-right {
  display: flex;
  align-items: center;
  gap: 40px;
  margin-left: 48px;
  width: 100%;
}
.header-contact {
  display: flex;
  gap: 32px;
  align-items: center;
}
.mainbar-nav {
  display: flex;
  align-items: center;
  gap: 36px;
  list-style: none;
  margin: 0;
  padding: 0;
  height: 100%;
  margin-left: auto;
}
.mainbar-nav li {
  color: #ff4d29;
  font-family: "Sora", sans-serif;
  font-size: 18px;
  font-weight: 700;
  padding: 0 18px 0 18px;
  cursor: pointer;
  height: 100%;
  display: flex;
  align-items: center;
  position: relative;
  background: none;
  border: none;
  transition: color 0.2s;
  z-index: 1;
}
.mainbar-nav li.active {
  color: #fff;
  font-weight: 800;
  z-index: 2;
}
.mainbar-nav li.active::before {
  content: "";
  position: absolute;
  left: -24px;
  top: 0;
  width: calc(100% + 48px);
  height: 100%;
  background: #ff4d29;
  clip-path: polygon(0 0, 100% 0, 100% 100%, 24px 100%, 0 56%);
  z-index: -1;
}
.mainbar-nav li.active::after {
  content: "";
  display: block;
  position: absolute;
  left: 24px;
  right: 24px;
  bottom: 10px;
  height: 3px;
  border-radius: 2px;
  background: #fff;
  z-index: 2;
}
.mainbar-nav li:hover:not(.active) {
  color: #ff4d29;
  opacity: 0.85;
}

.about-section {
  background: #fafbfc;
  padding: 64px 0 48px 0;
  /* Remove automatic animation - will be triggered by scroll */
  opacity: 1;
  transform: translateY(0);
}

/* Add scroll-triggered animation class */
.about-section.scroll-animate {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 1s ease-out forwards;
  animation-delay: 0.2s;
}

.about-container {
  max-width: 1800px;
  margin: 0 auto;
  display: flex;
  gap: 58px;
  align-items: flex-start;
  justify-content: space-between;
  padding: 0 40px;
}

.about-left {
  flex: 1.2;
  min-width: 340px;
  position: relative;
  min-height: 340px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

/* Scroll-triggered animation for left content */
.about-section.scroll-animate .about-left {
  animation: fadeInLeft 1.2s ease-out 0.5s both;
}

.about-watermark {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 120px;
  pointer-events: none;
  z-index: 0;
}

/* Scroll-triggered watermark animation */
.about-section.scroll-animate .about-watermark {
  opacity: 0;
  animation: fadeIn 2s ease-out 1s both;
}

@keyframes fadeIn {
  to {
    opacity: 0.04;
  }
}

.about-label,
.about-heading,
.about-desc,
.about-bullets {
  position: relative;
  z-index: 1;
}

.about-label {
  color: #ff4d29;
  font-family: "Sora", sans-serif;
  font-size: 22px;
  font-weight: 700;
  margin-bottom: 18px;
}

.about-heading {
  font-size: 2.2rem;
  font-weight: 800;
  color: #111;
  margin-bottom: 18px;
  line-height: 1.1;
}

.about-desc {
  font-size: 1rem;
  color: #222;
  margin-bottom: 24px;
  line-height: 1.6;
}

/* Scroll-triggered animations for about content */
.about-section.scroll-animate .about-label {
  animation: fadeInUp 0.8s ease-out 0.7s both;
}

.about-section.scroll-animate .about-heading {
  animation: fadeInUp 0.8s ease-out 0.9s both;
}

.about-section.scroll-animate .about-desc {
  animation: fadeInUp 0.8s ease-out 1.1s both;
}

.about-bullets {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.about-bullets li {
  font-size: 0.9rem;
  display: flex;
  align-items: flex-start;
  gap: 12px;
  /* Staggered animation for bullet points */
  opacity: 0;
  transform: translateX(-20px);
  animation: fadeInLeft 0.6s ease-out forwards;
}

.about-bullets li:nth-child(1) {
  animation-delay: 1.3s;
}

.about-bullets li:nth-child(2) {
  animation-delay: 1.5s;
}

.about-bullets li:nth-child(3) {
  animation-delay: 1.7s;
}

.about-bullet-icon {
  color: #ff4d29;
  font-size: 1.25em;
  min-width: 22px;
  min-height: 22px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  /* margin-top: 2px; */
  /* Icon bounce effect */
  transition: transform 0.3s ease;
}

.about-bullets li:hover .about-bullet-icon {
  transform: scale(1.2) rotate(10deg);
}

.about-right {
  flex: 1;
  min-width: 320px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32px;
  /* Right side animation */
  animation: fadeInRight 1.2s ease-out 0.8s both;
}

.about-stats-row {
  display: flex;
  gap: 36px;
  justify-content: center;
  margin-bottom: 24px;
}

.about-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  /* Individual stat animation */
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.8s ease-out forwards;
}

.about-stat:nth-child(1) {
  animation-delay: 1s;
}

.about-stat:nth-child(2) {
  animation-delay: 1.2s;
}

.about-stat:nth-child(3) {
  animation-delay: 1.4s;
}

.about-stat-outer {
  width: 78px;
  height: 78px;
  border: 2.5px solid #ff4d29;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  position: relative;
  /* Hover animation */
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.about-stat-outer:hover {
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 10px 25px rgba(255, 77, 41, 0.2);
  border-color: #ff270a;
}

.about-stat-outer::before {
  content: "";
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  border-radius: 50%;
  background: linear-gradient(45deg, #ff4d29, #ff270a);
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.about-stat-outer:hover::before {
  opacity: 0.1;
}

.about-stat-inner {
  width: 62px;
  height: 62px;
  border: 2.5px solid #ff4d29;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  transition: all 0.3s ease;
}

.about-stat-outer:hover .about-stat-inner {
  border-color: #ff270a;
  background: rgba(255, 77, 41, 0.05);
}

.about-stat-icon {
  font-size: 2.1rem;
  color: #ff4d29;
  transition: all 0.3s ease;
}

.about-stat-outer:hover .about-stat-icon {
  color: #ff270a;
  transform: scale(1.1);
}

.about-stat-label {
  color: #ff4d29;
  font-family: "Sora", sans-serif;
  font-size: 1.05rem;
  font-weight: 700;
  text-align: center;
  transition: color 0.3s ease;
}

.about-stat:hover .about-stat-label {
  color: #ff270a;
}

.about-image-block {
  width: 620px;
  height: 460px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 16px 0 rgba(0, 0, 0, 0.08);
  /* Image block animation */
  transition: all 0.4s ease;
}

.about-image-block:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.about-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  /* Image animation */
  transition: transform 0.4s ease;
}

.about-image-block:hover .about-image {
  transform: scale(1.05);
}
@media (max-width: 900px) {
  .about-container {
    flex-direction: column;
    gap: 32px;
    padding: 0 16px;
    align-items: center;
    text-align: center;
  }

  .about-left {
    min-width: auto;
    width: 100%;
    max-width: 600px;
  }

  .about-right {
    min-width: auto;
    width: 100%;
  }

  .about-image-block {
    width: 100%;
    max-width: 400px;
    height: 250px;
  }
}

.services-section {
  background: #fff;
  padding: 64px 0 64px 0;
}
.services-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
}
.services-label {
  color: #ff4d29;
  font-family: "Sora", sans-serif;
  font-size: 22px;
  font-weight: 700;
  margin-bottom: 18px;
}
.services-heading {
  font-family: "Montserrat", sans-serif;
  font-size: 3.2rem;
  font-weight: 800;
  color: #111;
  margin-bottom: 48px;
  line-height: 1.1;
}
.services-row {
  display: flex;
  gap: 40px;
  justify-content: center;
  align-items: stretch;
  flex-wrap: wrap;
}
.service-card {
  background: #fafbfc;
  border-radius: 18px;
  box-shadow: 0 2px 16px 0 rgba(0, 0, 0, 0.06);
  padding: 40px 32px 32px 32px;
  flex: 1 1 320px;
  max-width: 370px;
  min-width: 280px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: box-shadow 0.2s, transform 0.2s;
}
.service-card:hover {
  box-shadow: 0 6px 32px 0 rgba(255, 77, 41, 0.12);
  transform: translateY(-6px) scale(1.03);
}
.service-icon {
  width: 72px;
  height: 72px;
  border-radius: 50%;
  background: #fff;
  border: 2.5px solid #ff4d29;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  color: #ff4d29;
  margin-bottom: 24px;
}
.service-title {
  font-family: "Montserrat", sans-serif;
  font-size: 1.35rem;
  font-weight: 800;
  color: #222;
  margin-bottom: 14px;
}
.service-desc {
  font-family: "Sora", sans-serif;
  font-size: 1.08rem;
  color: #444;
  font-weight: 500;
  line-height: 1.6;
}
@media (max-width: 900px) {
  .services-row {
    flex-direction: column;
    gap: 25px;
    align-items: center;
  }

  .service-card {
    max-width: 400px;
    width: 100%;
  }

  .services-heading {
    font-size: 2.4rem;
  }

  .services-container {
    padding: 0 20px;
  }
}

.services-gallery-section {
  position: relative;
  padding: 80px 40px;
  background-color: #b1aaaa14;
  overflow: hidden;
  margin-bottom: 50px;
}

/* Scroll-triggered animation for services section */
.services-gallery-section.scroll-animate {
  opacity: 0;
  transform: translateY(50px);
  animation: fadeInUp 1.2s ease-out 0.3s forwards;
}

.services-gallery-container {
  position: relative;
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
}

.services-gallery-watermark {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 120px;
  pointer-events: none;
  z-index: 0;
  /* Watermark fade animation */
  opacity: 0;
  animation: fadeIn 2s ease-out 1.5s both;
}

.services-gallery-label {
  color: #ff4d29;
  font-family: "Sora", sans-serif;
  font-size: 22px;
  font-weight: 700;
  margin-bottom: 18px;
  position: relative;
  z-index: 1;
  /* Label animation */
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.8s ease-out 0.6s forwards;
}

.services-gallery-heading {
  font-family: "Montserrat", sans-serif;
  font-size: 2.8rem;
  font-weight: 800;
  color: #111;
  margin-bottom: 12px;
  line-height: 1.1;
  position: relative;
  z-index: 1;
  /* Heading animation */
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.8s ease-out 0.8s forwards;
}

.services-gallery-underline {
  width: 80px;
  height: 5px;
  background: #ff4d29;
  border-radius: 3px;
  margin: 20px auto 40px auto;
  position: relative;
  z-index: 1;
  /* Underline animation */
  opacity: 0;
  transform: scaleX(0);
  animation: slideInScale 0.8s ease-out 1s forwards;
}

@keyframes slideInScale {
  to {
    opacity: 1;
    transform: scaleX(1);
  }
}

.services-gallery-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(2, 320px);
  gap: 30px;
  position: relative;
  z-index: 1;
  margin-top: 40px;
}

.services-gallery-card {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 16px 0 rgba(0, 0, 0, 0.08);
  background: #fff;
  min-width: 0;
  min-height: 0;
  display: flex;
  align-items: flex-end;
  height: 100%;
  /* Card animation */
  opacity: 0;
  transform: translateY(40px) scale(0.95);
  animation: cardFadeIn 0.8s ease-out forwards;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Staggered animation for cards */
.services-gallery-card:nth-child(1) {
  animation-delay: 1.2s;
}
.services-gallery-card:nth-child(2) {
  animation-delay: 1.4s;
}
.services-gallery-card:nth-child(3) {
  animation-delay: 1.6s;
}
.services-gallery-card:nth-child(4) {
  animation-delay: 1.8s;
}
.services-gallery-card:nth-child(5) {
  animation-delay: 2s;
}

@keyframes cardFadeIn {
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.services-gallery-card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.services-gallery-card img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  transition: transform 0.4s ease;
}

.services-gallery-card:hover img {
  transform: scale(1.1);
}

.services-gallery-overlay {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(0deg, #ff4d29e6 80%, #ff4d2900 100%);
  color: #fff;
  padding: 28px 22px 18px 22px;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  /* Overlay animation */
  transform: translateY(20px);
  transition: transform 0.3s ease;
}

.services-gallery-card:hover .services-gallery-overlay {
  transform: translateY(0);
}

.services-gallery-title {
  font-family: "Montserrat", sans-serif;
  font-size: 1.25rem;
  font-weight: 800;
  margin-bottom: 6px;
  /* Title animation */
  transform: translateY(10px);
  transition: transform 0.3s ease;
}

.services-gallery-card:hover .services-gallery-title {
  transform: translateY(0);
}

.services-gallery-desc {
  font-family: "Sora", sans-serif;
  font-size: 1.02rem;
  font-weight: 500;
  line-height: 1.5;
  /* Description animation */
  transform: translateY(10px);
  opacity: 0.9;
  transition: all 0.3s ease;
}

.services-gallery-card:hover .services-gallery-desc {
  transform: translateY(0);
  opacity: 1;
}

.services-gallery-viewmore {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  /* View more animation */
  opacity: 0;
  transform: translateY(40px) scale(0.9);
  animation: cardFadeIn 0.8s ease-out 2.2s forwards;
}

.services-gallery-viewmore-glow {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 180px;
  height: 180px;
  background: radial-gradient(circle, #ff4d2940 0%, #fff0 80%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  z-index: 0;
  /* Glow pulse animation */
  animation: glowPulse 3s ease-in-out infinite;
}

@keyframes glowPulse {
  0%,
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.6;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.1);
    opacity: 0.8;
  }
}

.services-gallery-viewmore-btn {
  position: relative;
  z-index: 1;
  background: #ff4d29;
  color: #fff;
  font-family: "Montserrat", sans-serif;
  font-size: 1.2rem;
  font-weight: 700;
  border: none;
  border-radius: 8px;
  padding: 18px 44px;
  /* Button animations */
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateZ(0);
}

.services-gallery-viewmore-btn:hover {
  background: #ff270a;
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 10px 25px rgba(255, 77, 41, 0.4);
}

.services-gallery-viewmore-btn:active {
  transform: translateY(-1px) scale(1.02);
}

@media (max-width: 1100px) {
  .services-gallery-grid {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(3, 280px);
    gap: 80px;
  }

  .services-gallery-card {
    height: 100%;
  }
}

@media (max-width: 700px) {
  .services-gallery-section {
    padding: 40px 15px;
  }

  .services-gallery-grid {
    grid-template-columns: 1fr;
    grid-template-rows: repeat(6, 220px);
    gap: 24px;
  }

  .services-gallery-heading {
    font-size: 1.8rem;
  }

  .services-gallery-container {
    max-width: 100%;
    padding: 0;
  }
}

.contact-hero-section {
  position: relative;
  min-height: 420px;
  height: 60vh; /* Adjust height as needed for the image */
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  text-align: center;
}

/* Scroll-triggered animation for contact hero */
.contact-hero-section.scroll-animate {
  opacity: 0;
  transform: translateY(50px);
  animation: fadeInUp 1.2s ease-out 0.4s forwards;
}

.contact-hero-bg-img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 0;
  /* Background image animation */
  animation: heroZoom 15s ease-in-out infinite alternate;
  transform: scale(1.05);
}

.contact-hero-bg-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    #000 0%,
    #0000 70%
  ); /* Adjust gradient to match image */
  opacity: 0.7;
  z-index: 1;
  /* Overlay animation */
  animation: overlayFade 1.5s ease-out 0.5s both;
}

.contact-hero-content {
  position: relative;
  z-index: 2;
  color: #fff;
  max-width: 800px; /* Adjust max-width as needed */
  padding: 20px;
}

/* Scroll-triggered animations for contact hero content */
.contact-hero-section.scroll-animate .contact-hero-content {
  animation: fadeInUp 1.2s ease-out 0.8s both;
}

.contact-hero-section.scroll-animate .contact-hero-content h1 {
  animation: fadeInUp 1s ease-out 1s both;
}

.contact-hero-section.scroll-animate .contact-hero-content p {
  animation: fadeInUp 1s ease-out 1.2s both;
}

.contact-hero-content h1 {
  font-family: "Montserrat", sans-serif;
  font-size: 2.8rem;
  font-weight: 800;
  margin-bottom: 18px;
  line-height: 1.1;
  color: #fff; /* Ensure white color */
  /* Heading animation */
  animation: fadeInUp 1s ease-out 1s both;
}

.contact-hero-content .highlight {
  color: #ff270a;
  /* Highlight glow effect */
  text-shadow: 0 0 15px rgba(255, 39, 10, 0.4);
  animation: textGlow 3s ease-in-out infinite alternate;
}

.contact-hero-content p {
  font-family: "Sora", sans-serif;
  font-size: 1.1rem;
  font-weight: 400;
  color: #fff;
  margin: 0 auto 30px auto; /* Center paragraph and add margin-bottom */
  line-height: 1.5;
  max-width: 600px; /* Limit paragraph width */
  /* Paragraph animation */
  animation: fadeInUp 1s ease-out 1.2s both;
}

.contact-hero-btn {
  background: #ff4d29;
  color: #fff;
  font-family: "Montserrat", sans-serif;
  font-size: 1.2rem;
  font-weight: 700;
  border: none;
  border-radius: 8px;
  padding: 18px 44px;
  cursor: pointer;
  box-shadow: 0 2px 16px 0 rgba(255, 77, 41, 0.1);
  /* Button animation */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 1s ease-out 1.4s forwards;
}

.contact-hero-btn:hover {
  background: #ff270a;
  box-shadow: 0 6px 32px 0 rgba(255, 77, 41, 0.18);
  transform: translateY(-3px) scale(1.05);
}

.contact-hero-btn:active {
  transform: translateY(-1px) scale(1.02);
}

/* Clients Section Styles */
.clients-section {
  padding: 80px 0;
  background-color: #f8f8f8;
  text-align: center;
}

/* Scroll-triggered animation for clients section */
.clients-section.scroll-animate {
  opacity: 0;
  transform: translateY(50px);
  animation: fadeInUp 1.2s ease-out 0.3s forwards;
}

.clients-container {
  max-width: 100%;
  margin: 0;
  padding: 0;
}

.clients-label {
  color: #ff4d29;
  font-family: "Sora", sans-serif;
  font-size: 22px;
  font-weight: 700;
  margin-bottom: 18px;
}

.clients-heading {
  font-family: "Montserrat", sans-serif;
  font-size: 2.2rem;
  font-weight: 800;
  color: #111;
  margin-bottom: 60px;
  line-height: 1.1;
}

/* Scroll-triggered animations for clients content */
.clients-section.scroll-animate .clients-label {
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.8s ease-out 0.6s forwards;
}

.clients-section.scroll-animate .clients-heading {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.8s ease-out 0.8s forwards;
}

.clients-carousel {
  width: 100%;
  overflow: hidden;
  position: relative;
  margin: 40px 0;
  /* Carousel container animation */
  opacity: 1;
  transform: none;
  /* animation: fadeInUp 1s ease-out 1s forwards; */
}

.clients-track {
  display: flex;
  align-items: center;
  gap: 60px;
  animation: scrollLeft 30s linear infinite;
  width: calc(200% + 60px * 13); /* Width for 14 logos + gaps */
}

@keyframes scrollLeft {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(
      calc(-50% - 30px)
    ); /* Move by half width + half gap */
  }
}

.client-logo {
  flex-shrink: 0;
  width: 160px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  padding: 15px;
  transition: all 0.3s ease;
  opacity: 0.8;
}

.client-logo:hover {
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  opacity: 1;
}

.client-logo img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  filter: grayscale(100%);
  transition: filter 0.3s ease;
}

.client-logo:hover img {
  filter: grayscale(0%);
}

/* Pause animation on hover */
.clients-carousel:hover .clients-track {
  animation-play-state: paused;
}

/* Clients Carousel Mobile Responsive */
@media (max-width: 768px) {
  .clients-track {
    gap: 40px;
    animation-duration: 25s; /* Slightly faster on mobile */
  }

  .client-logo {
    width: 140px;
    height: 70px;
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .clients-track {
    gap: 30px;
    animation-duration: 20s; /* Even faster on small mobile */
  }

  .client-logo {
    width: 120px;
    height: 60px;
    padding: 10px;
  }
}

@media (max-width: 375px) {
  .clients-track {
    gap: 25px;
    animation-duration: 18s;
  }

  .client-logo {
    width: 100px;
    height: 50px;
    padding: 8px;
  }
}

/* ===============================================
   ADDITIONAL FIXES FOR BETTER MOBILE EXPERIENCE
   =============================================== */

/* Ensure proper section spacing */
.hero-section,
.about-section,
.services-section,
.services-gallery-section,
.contact-hero-section,
.clients-section,
.testimonials-section {
  width: 100%;
  overflow: hidden;
}

/* Fix any potential text overflow */
* {
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Ensure images don't exceed container width */
img {
  max-width: 100%;
  height: auto;
}

/* Fix container max-widths for very small screens */
@media (max-width: 320px) {
  .hero-content {
    padding: 8px 0 0 10px;
    max-width: 90%;
  }

  .hero-content h1 {
    font-size: 1.2rem;
  }

  .hero-content p {
    font-size: 0.8rem;
  }

  .about-label {
    font-size: 12px;
    margin-bottom: 8px;
  }

  .about-heading {
    font-size: 1.2rem;
  }

  .about-desc {
    font-size: 0.5rem;
  }

  .services-gallery-heading {
    font-size: 1.2rem;
  }

  .contact-hero-content h1 {
    font-size: 1.2rem;
  }

  .clients-heading {
    font-size: 1.2rem;
  }
}

/* Custom Clients Carousel */
.custom-clients-carousel {
  width: 100%;
  overflow: hidden;
  margin: 40px 0;
  background: transparent;
}
.custom-clients-track {
  display: flex;
  align-items: center;
  gap: 60px;
  animation: customScrollRight 18s linear infinite;
  width: max-content;
}
@keyframes customScrollRight {
  0% {
    transform: translateX(-50%);
  }
  100% {
    transform: translateX(0);
  }
}
.custom-client-img {
  width: 220px;
  height: 120px;
  object-fit: contain;
  /* Removed background, border-radius, and box-shadow */
  padding: 0;
  opacity: 1;
  filter: none;
  transition: transform 0.3s;
  display: block;
}
.custom-client-img:hover {
  transform: scale(1.08);
  /* No box-shadow */
}
@media (max-width: 1024px) {
  .custom-client-img {
    width: 160px;
    height: 90px;
  }
}
@media (max-width: 768px) {
  .custom-client-img {
    width: 120px;
    height: 60px;
  }
  .custom-clients-track {
    gap: 30px;
  }
}
