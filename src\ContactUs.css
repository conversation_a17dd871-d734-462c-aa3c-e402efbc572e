/* ===============================================
   ANIMATION KEYFRAMES AND UTILITIES
   =============================================== */

/* Smooth scrolling for the entire page */
html {
  scroll-behavior: smooth;
}

/* Global animation keyframes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-40px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(40px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Performance optimizations for animations */
*,
*::before,
*::after {
  backface-visibility: hidden;
  transform-style: preserve-3d;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Page load animation */
.contact-us-page {
  opacity: 0;
  animation: fadeIn 0.8s ease-out 0.2s forwards;
}

/* ContactUs.css */

/* Global overflow prevention */
* {
  box-sizing: border-box;
}

.contact-us-page {
  width: 100%;
  overflow-x: hidden;
  max-width: 100vw;
}

.contact-section {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 80px 40px;
  background-color: #f5f5f5d9;
  gap: 50px;
  position: relative;
  width: 100%;
  max-width: 100vw;
  box-sizing: border-box;
  overflow: hidden;
  min-height: 80vh;
}

/* Scroll-triggered animation for contact section */
.contact-section.scroll-animate {
  animation: fadeInUp 1.2s ease-out 0.3s forwards;
}

.contact-content {
  flex: 1;
  max-width: 600px;
  text-align: left;
  position: relative;
}

/* Scroll-triggered animation for content */
.contact-section.scroll-animate .contact-content {
  animation: fadeInLeft 1.2s ease-out 0.6s both;
}

.contact-content::before {
  content: "Contact Us";
  position: absolute;
  top: -20px;
  left: -20px;
  font-size: 6rem;
  font-weight: 800;
  color: rgba(0, 0, 0, 0.05);
  pointer-events: none;
  user-select: none;
  z-index: 0;
  white-space: nowrap;
  overflow: hidden;
  max-width: 100%;
}

.contact-label {
  font-family: "Montserrat", sans-serif;
  font-size: 1.5rem;
  font-weight: 600;
  color: #ff270a;
  margin-bottom: 15px;
  position: relative;
  display: inline-block;
  z-index: 1;
}

/* Scroll-triggered animation for label */
.contact-section.scroll-animate .contact-label {
  animation: fadeInUp 0.8s ease-out 0.8s both;
}

.contact-heading {
  font-family: "Montserrat", sans-serif;
  font-size: 3rem;
  font-weight: 800;
  color: #000;
  margin-bottom: 25px;
  line-height: 1.2;
  position: relative;
  z-index: 1;
}

/* Scroll-triggered animation for heading */
.contact-section.scroll-animate .contact-heading {
  animation: fadeInUp 0.8s ease-out 1s both;
}

.contact-heading .highlight {
  color: #ff270a;
}

.contact-description {
  font-family: "Sora", sans-serif;
  font-size: 1.1rem;
  color: #535353;
  margin-bottom: 40px;
  line-height: 1.6;
  position: relative;
  z-index: 1;
}

.contact-info {
  margin-bottom: 40px;
  position: relative;
  z-index: 1;
}

.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
  font-family: "Sora", sans-serif;
  font-size: 1rem;
  color: #535353;
  gap: 15px;
}

.info-icon {
  color: #ff270a;
  font-size: 1.2rem;
  margin-top: 2px;
  flex-shrink: 0;
}

.info-item span {
  font-weight: 500;
  line-height: 1.5;
}

.get-in-touch-button {
  background-color: #ff270a;
  color: #fff;
  border: none;
  padding: 18px 35px;
  font-size: 1.1rem;
  font-weight: 700;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
  box-shadow: 0 4px 15px rgba(255, 39, 10, 0.2);
}

.get-in-touch-button:hover {
  background-color: #e02000;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 39, 10, 0.3);
}

.contact-image-container {
  flex: 1;
  max-width: 500px;
  position: relative;
  overflow: visible;
}

.contact-image {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 8px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Red bar on the right of the image - Fixed positioning */
.contact-image-container::after {
  content: "";
  position: absolute;
  top: 15%;
  right: -15px;
  width: 30px;
  height: 70%;
  background-color: #ff270a;
  z-index: -1;
  border-radius: 4px;
  max-width: calc(100vw - 100%);
}

@media (max-width: 900px) {
  .contact-section {
    flex-direction: column;
    padding: 60px 30px;
    gap: 30px;
  }

  .contact-content,
  .contact-image-container {
    max-width: 100%;
  }

  .contact-content::before {
    font-size: 5rem;
    top: -10px;
    left: -10px;
  }

  .contact-image-container::after {
    right: -10px;
    width: 25px;
    height: 70%;
  }

  .contact-heading {
    font-size: 2.5rem;
  }
}

@media (max-width: 768px) {
  .contact-section {
    padding: 50px 25px;
    gap: 25px;
  }

  .contact-heading {
    font-size: 2.2rem;
  }

  .contact-label {
    font-size: 1.3rem;
  }

  .contact-description {
    font-size: 1rem;
  }

  .contact-content::before {
    font-size: 4.5rem;
    top: -5px;
    left: -5px;
  }

  .contact-image-container::after {
    right: -8px;
    width: 20px;
    height: 65%;
  }

  .get-in-touch-button {
    padding: 15px 30px;
    font-size: 1rem;
  }
}

@media (max-width: 600px) {
  .contact-section {
    padding: 40px 20px;
    gap: 20px;
  }

  .contact-heading {
    font-size: 2rem;
    margin-bottom: 20px;
  }

  .contact-label {
    font-size: 1.2rem;
    margin-bottom: 12px;
  }

  .contact-content::before {
    font-size: 4rem;
    top: 0;
    left: 0;
  }

  .info-item,
  .contact-description {
    font-size: 0.95rem;
  }

  .info-icon {
    font-size: 1.1rem;
    margin-top: 1px;
  }

  .get-in-touch-button {
    padding: 14px 28px;
    font-size: 0.95rem;
  }

  .contact-image-container::after {
    right: -6px;
    width: 18px;
    height: 60%;
  }
}

@media (max-width: 480px) {
  .contact-section {
    padding: 35px 15px;
    gap: 15px;
  }

  .contact-heading {
    font-size: 1.8rem;
    line-height: 1.3;
  }

  .contact-label {
    font-size: 1.1rem;
  }

  .contact-description {
    font-size: 0.9rem;
    margin-bottom: 30px;
  }

  .contact-content::before {
    font-size: 3.5rem;
    opacity: 0.8;
  }

  .info-item {
    font-size: 0.9rem;
    margin-bottom: 15px;
    gap: 12px;
  }

  .info-icon {
    font-size: 1rem;
  }

  .get-in-touch-button {
    padding: 12px 25px;
    font-size: 0.9rem;
    width: 100%;
    max-width: 300px;
  }

  .contact-image-container::after {
    right: -4px;
    width: 15px;
    height: 55%;
  }
}

@media (max-width: 375px) {
  .contact-section {
    padding: 30px 12px;
  }

  .contact-heading {
    font-size: 1.6rem;
  }

  .contact-label {
    font-size: 1rem;
  }

  .contact-description {
    font-size: 0.85rem;
    margin-bottom: 25px;
  }

  .contact-content::before {
    font-size: 3rem;
    opacity: 0.6;
  }

  .info-item {
    font-size: 0.85rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .info-icon {
    margin-bottom: 3px;
  }

  .get-in-touch-button {
    padding: 10px 20px;
    font-size: 0.85rem;
  }

  .contact-image-container::after {
    display: none; /* Hide decorative element on very small screens */
  }
}

@media (max-width: 320px) {
  .contact-section {
    padding: 25px 10px;
  }

  .contact-heading {
    font-size: 1.4rem;
    margin-bottom: 15px;
  }

  .contact-label {
    font-size: 0.95rem;
    margin-bottom: 10px;
  }

  .contact-description {
    font-size: 0.8rem;
    margin-bottom: 20px;
  }

  .contact-content::before {
    font-size: 2.5rem;
    opacity: 0.5;
  }

  .info-item {
    font-size: 0.8rem;
    margin-bottom: 12px;
  }

  .get-in-touch-button {
    padding: 8px 18px;
    font-size: 0.8rem;
  }
}

.italic {
  font-style: italic;
}
