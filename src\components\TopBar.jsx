import React from "react";
import {
  <PERSON>a<PERSON>ace<PERSON><PERSON>,
  <PERSON>a<PERSON><PERSON><PERSON>,
  FaInstagram,
  FaLinkedinIn,
} from "react-icons/fa";
import "./Navbar.css"; // Assuming top-bar styles might be in Navbar.css or a shared CSS

const TopBar = () => {
  return (
    <div className="top-bar">
      <div className="top-bar-content">
        <span className="top-bar-welcome">
          Welcome to <span className="brand-highlight">Gait</span> Engineers
        </span>
        <div className="top-bar-icons">
          <a href="#" aria-label="Facebook" className="social-icon">
            <FaFacebookF />
          </a>
          <a href="#" aria-label="Twitter" className="social-icon">
            <FaTwitter />
          </a>
          <a href="#" aria-label="Instagram" className="social-icon">
            <FaInstagram />
          </a>
          <a href="#" aria-label="LinkedIn" className="social-icon">
            <FaLinkedinIn />
          </a>
        </div>
      </div>
    </div>
  );
};

export default TopBar;
