import React, { useEffect } from "react";
import Navbar from "./components/Navbar";
import Footer from "./components/Footer";
import TopBar from "./components/TopBar";
import "./AboutUs.css";
import ironPourImg from "./images/iron-pour.jpg";
import founderImg from "./images/founder.jpg";
import visionImg from "./images/our-vission.jpg";
import missionImg from "./images/our-mission.jpg";
import { FiPhone, FiMail, FiUserCheck, FiAward, FiClock } from "react-icons/fi";
import { FaCheckCircle, FaStar } from "react-icons/fa";
import {
  FaHandshake,
  FaTools,
  FaShieldAlt,
  FaLightbulb,
  FaDollarSign,
  FaCogs,
} from "react-icons/fa";
import aboutWorkerImg from "./images/about_worker.jpg";

const AboutUs = () => {
  // Scroll-triggered animation effect
  useEffect(() => {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: "0px 0px -50px 0px",
    };

    const observer = new IntersectionObserver(entries => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add("scroll-animate");
        }
      });
    }, observerOptions);

    // Observe all sections that should have scroll animations
    const sectionsToAnimate = document.querySelectorAll(
      ".about-who-we-are-section, .founder-story-section, .our-vision-section, .our-mission-section, .achievements-section"
    );

    sectionsToAnimate.forEach(section => {
      observer.observe(section);
    });

    // Cleanup observer on component unmount
    return () => {
      sectionsToAnimate.forEach(section => {
        observer.unobserve(section);
      });
    };
  }, []);

  return (
    <>
      <TopBar />
      <Navbar />
      <div className="about-us-page-content">
        {/* Who We Are Section */}
        <section className="about-who-we-are-section">
          <div className="about-who-we-are-content">
            <h2 className="about-who-we-are-heading">
              <span className="about-who-we-are-heading-span">Who We Are</span>
            </h2>
            <div className="about-who-we-are-underline"></div>
            <h3 className="about-who-we-are-subheading">
              Driven by Experience, Defined by Precision
            </h3>
            <p className="about-who-we-are-text">
              At Gait Engineers, we specialize in delivering high-quality,
              cost-effective solutions in non-ferrous castings and Special
              Purpose Machines (SPM). With over 20 years of expertise, we
              provide comprehensive services from feasibility studies to project
              execution, ensuring precision, efficiency, and customer
              satisfaction.
            </p>
          </div>
          <div className="about-who-we-are-image-container">
            <div className="about-who-we-are-image-overlay"></div>
            <img
              src={ironPourImg}
              alt="Who We Are Background"
              className="about-who-we-are-image"
            />
          </div>
        </section>

        {/* Founder's Story Section */}
        <section className="founder-story-section">
          <div className="founder-story-content">
            <div className="founder-story-label">Founder's Story</div>
            <h2 className="founder-story-heading">
              Engineering Excellence,
              <br />
              Led by Vision
            </h2>
            <div className="founder-story-underline"></div>
            <p className="founder-story-text">
              R.C. Ramesh Babu, the driving force behind Gait Engineers, brings
              over 20 years of expertise in castings and SPM solutions. With a
              deep understanding of manufacturing and a passion for precision,
              he founded Gait Engineers to deliver reliable, efficient, and
              innovative solutions. His leadership ensures every project meets
              the highest standards of quality and professionalism.
            </p>
          </div>
          <div className="founder-story-image-container">
            <div className="founder-story-red-corner-top-right">
              <div className="horizontal-bar"></div>
              <div className="vertical-bar"></div>
            </div>
            <div className="founder-story-red-corner-bottom-left">
              <div className="horizontal-bar"></div>
              <div className="vertical-bar"></div>
            </div>
            <img
              src={founderImg}
              alt="Founder R.C. Ramesh Babu"
              className="founder-story-image"
            />
          </div>
        </section>

        {/* Our Vision Section */}
        <section className="our-vision-section">
          <div className="our-vision-image-container">
            <img
              src={visionImg}
              alt="Our Vision"
              className="our-vision-image"
            />
          </div>
          <div className="our-vision-content">
            <div className="our-vision-label">Our Vision</div>
            <h2 className="our-vision-heading">
              Shaping the Future of Engineering with Innovation and Precision
            </h2>
            <div className="our-vision-text">
              To be a leading provider of innovative, high-quality engineering
              solutions, setting benchmarks for precision and customer
              satisfaction in the manufacturing industry.
            </div>
          </div>
        </section>

        {/* Our Mission Section */}
        <section className="our-mission-section">
          <div className="our-mission-content">
            <div className="our-mission-label">Our Mission</div>
            <h2 className="our-mission-heading">
              Shaping the Future of Engineering with Innovation and Precision
            </h2>
            <ul className="our-mission-list">
              <li>
                <span className="bullet-icon"></span>
                To deliver advanced engineering solutions with a focus on
                quality, efficiency, and innovation.
              </li>
              <li>
                <span className="bullet-icon"></span>
                To build lasting relationships by understanding and exceeding
                client expectations.
              </li>
              <li>
                <span className="bullet-icon"></span>
                To improve and adopt to technologies to provide the best
                possible solutions.
              </li>
            </ul>
          </div>
          <div className="our-mission-image-container">
            <img
              src={missionImg}
              alt="Our Mission"
              className="our-mission-image"
            />
          </div>
        </section>

        {/* Achievements Section */}
        <section className="achievements-section">
          <div className="achievements-header">
            <div className="achievements-label">Achievements</div>
            <h2 className="achievements-heading">
              Shaping a future of engineering excellence through groundbreaking
              <br />
              achievements and unwavering dedication
            </h2>
          </div>
          <div className="achievements-grid">
            <div className="achievement-card">
              <div className="achievement-icon-container">
                <FaHandshake className="achievement-icon" />
              </div>
              <h3 className="achievement-title">Industry Partnerships</h3>
              <p className="achievement-description">
                Collaborated with leading industrial, automotive, and aerospace
                companies, delivering tailored engineering solutions.
              </p>
            </div>
            <div className="achievement-card">
              <div className="achievement-icon-container">
                <FaTools className="achievement-icon" />
              </div>
              <h3 className="achievement-title">Customized Automation</h3>
              <p className="achievement-description">
                Designed and developed innovative solutions to improve
                automation, enhance operational efficiency, optimize processes.
              </p>
            </div>
            <div className="achievement-card">
              <div className="achievement-icon-container">
                <FaShieldAlt className="achievement-icon" />
              </div>
              <h3 className="achievement-title">Strict Quality Control</h3>
              <p className="achievement-description">
                Adhered to global quality standards, maintaining rigorous
                quality control throughout the manufacturing cycle.
              </p>
            </div>
            <div className="achievement-card">
              <div className="achievement-icon-container">
                <FaLightbulb className="achievement-icon" />
              </div>
              <h3 className="achievement-title">Investment In Innovation</h3>
              <p className="achievement-description">
                Continuously invested in R&D, smart manufacturing, and
                future-ready technologies to stay ahead of industry demands.
              </p>
            </div>
            <div className="achievement-card">
              <div className="achievement-icon-container">
                <FaDollarSign className="achievement-icon" />
              </div>
              <h3 className="achievement-title">Cost Effective Solution</h3>
              <p className="achievement-description">
                Providing high-quality engineering solutions that optimize
                resources, reduce operational costs, and deliver maximum value.
              </p>
            </div>
            <div className="achievement-card">
              <div className="achievement-icon-container">
                <FaCogs className="achievement-icon" />
              </div>
              <h3 className="achievement-title">Tailored Solution</h3>
              <p className="achievement-description">
                Delivering tailored engineering solutions that meet unique
                client needs, ensuring flexibility, precision, across every
                project.
              </p>
            </div>
          </div>
        </section>
      </div>
      <Footer />
    </>
  );
};

export default AboutUs;
