/* ===============================================
   ANIMATION KEYFRAMES AND UTILITIES
   =============================================== */

/* Smooth scrolling for the entire page */
html {
  scroll-behavior: smooth;
}

/* Global animation keyframes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-40px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(40px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scaleX(0);
  }
  to {
    opacity: 1;
    transform: scaleX(1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Performance optimizations for animations */
*,
*::before,
*::after {
  backface-visibility: hidden;
  transform-style: preserve-3d;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

body {
  background: #f5f5f5;
  opacity: 0;
  animation: fadeIn 0.8s ease-out 0.2s forwards;
}

.services-section {
  display: flex;
  padding: 10px 40px 0 40px;
  background: #ffffff;
  min-height: unset;
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
  gap: 60px;
  justify-content: center;
  align-items: flex-start;
}

/* Scroll-triggered animation for services section */
.services-section.scroll-animate {
  animation: fadeInUp 1.2s ease-out 0.3s forwards;
}

.services-sidebar {
  width: 400px;
  padding: 0;
  background: none;
  border-right: none;
  display: flex;
  flex-direction: column;
  gap: 20px;
  min-width: 280px;
  max-width: 450px;
  padding-right: 40px;
  position: relative;
  flex-shrink: 0;
}

/* Scroll-triggered animation for sidebar */
.services-section.scroll-animate .services-sidebar {
  animation: fadeInLeft 1.2s ease-out 0.6s both;
}

.services-sidebar::after {
  content: "";
  position: absolute;
  right: -30px;
  top: 0;
  width: 4px;
  height: 80%;
  background-color: #ff4d29;
  margin-top: 40px;
  border-radius: 5px;
}

.services-label {
  color: #ff4d29;
  font-size: 1.1rem;
  font-weight: 700;
  margin-bottom: 180px;
  letter-spacing: 0.5px;
  text-align: left;
  font-family: "Montserrat", sans-serif;
}

.services-categories {
  list-style: none;
  padding: 0;
  margin: 0;
  font-family: "Sora", sans-serif;
  font-size: 1rem;
  font-weight: 500;
}

/* Staggered animations for category items */
.services-section.scroll-animate .services-categories > li:nth-child(1) {
  animation: fadeInLeft 0.8s ease-out 0.8s both;
}

.services-section.scroll-animate .services-categories > li:nth-child(2) {
  animation: fadeInLeft 0.8s ease-out 1s both;
}

.services-section.scroll-animate .services-categories > li:nth-child(3) {
  animation: fadeInLeft 0.8s ease-out 1.2s both;
}

.services-section.scroll-animate .services-categories > li:nth-child(4) {
  animation: fadeInLeft 0.8s ease-out 1.4s both;
}

.category-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #ff4d29;
  margin-bottom: 15px;
  display: block;
  font-family: "Montserrat", sans-serif;
  line-height: 1.3;
}

.services-categories > li {
  margin-bottom: 30px;
  color: #ff4d29;
  cursor: pointer;
  font-weight: 600;
  font-size: 1.1rem;
  line-height: 1.4;
  transition: all 0.3s ease;
  padding: 10px 0;
  border-radius: 5px;
}

.services-categories > li:hover {
  transform: translateX(5px);
  color: #ff270a;
}

.services-categories > li.active {
  color: #ff4d29;
  font-weight: 700;
}

.services-categories ul {
  list-style: none;
  padding-left: 0;
  margin-top: 15px;
}

.services-categories ul li {
  color: #666;
  font-weight: 400;
  margin-bottom: 10px;
  cursor: pointer;
  font-size: 1rem;
  position: relative;
  padding: 5px 0;
  transition: all 0.3s ease;
  line-height: 1.4;
}

.services-categories ul li:hover {
  color: #333;
  transform: translateX(3px);
}

.services-categories ul li.active {
  color: #000;
  font-weight: 800;
  text-decoration: none;
}

.services-categories ul li .underlined {
  text-decoration: underline;
  text-decoration-color: #ff4d29;
  text-decoration-thickness: 5px;
  text-underline-offset: 3px;
  border-radius: 10px;
}

.services-categories > li:nth-child(n + 2) {
  color: #ff4d29;
  font-weight: 600;
  font-size: 1.1rem;
  margin-top: 0;
}

.services-content {
  flex: 1;
  padding: 0 40px 0 20px;
  display: flex;
  flex-direction: column;
  min-width: 0;
  position: relative;
  z-index: 2;
  background: #ffffff;
  border-radius: 8px;
  max-width: 800px;
}

/* Scroll-triggered animation for content */
.services-section.scroll-animate .services-content {
  animation: fadeInRight 1.2s ease-out 0.8s both;
}

.services-header-flex-row {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 1400px;
  margin: 0 auto 40px auto;
  padding: 10px 40px 0 40px;
  gap: 200px;
  background: #ffffff;
  margin-top: 60px;
}

/* Scroll-triggered animation for header */
.services-header-flex-row.scroll-animate {
  animation: fadeInUp 1.2s ease-out 0.3s forwards;
}

.services-title-main {
  font-family: "Montserrat", sans-serif;
  font-size: 2.1rem;
  font-weight: 800;
  color: #ff4d29;
  margin: 0;
  letter-spacing: 0.5px;
  background: none;
  text-align: left;
  flex-shrink: 0;
}

/* Scroll-triggered animation for title */
.services-header-flex-row.scroll-animate .services-title-main {
  animation: fadeInLeft 0.8s ease-out 0.6s both;
}

.services-watermark {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  font-size: 3.5rem;
  font-weight: 800;
  color: #f2f2f2;
  opacity: 0.6;
  z-index: 1;
  pointer-events: none;
  user-select: none;
  letter-spacing: 1px;
  font-family: "Montserrat", sans-serif;
  line-height: 1;
  white-space: nowrap;
}

.search-container {
  position: relative;
  display: flex;
  align-items: center;
  z-index: 2;
}

/* Scroll-triggered animation for search container */
.services-header-flex-row.scroll-animate .search-container {
  animation: fadeInRight 0.8s ease-out 0.8s both;
}

.search-icon {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
  font-size: 18px;
  z-index: 1;
}

.services-search {
  width: 600px;
  max-width: 600px;
  padding: 15px 20px 15px 50px;
  border: 2px solid #e0e0e0bb;
  border-radius: 50px;
  font-size: 1rem;
  outline: none;
  background: #ffffff54;
  color: #333;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  font-family: "Sora", sans-serif;
}

.services-search:focus {
  border-color: #ff4d29;
  box-shadow: 0 4px 15px rgba(255, 77, 41, 0.2);
  transform: translateY(-2px);
}

.services-search:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.services-search::placeholder {
  color: #999;
}

.services-details {
  background: none;
  z-index: 1;
  position: relative;
  text-align: left;
  margin-top: 35px;
}

/* Scroll-triggered animation for details */
.services-section.scroll-animate .services-details {
  animation: fadeInUp 0.8s ease-out 1s both;
}

.services-details h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 25px;
  font-family: "Montserrat", sans-serif;
  line-height: 1.2;
}

/* Scroll-triggered animation for heading */
.services-section.scroll-animate .services-details h2 {
  animation: fadeInUp 0.8s ease-out 1.2s both;
}

.services-details .highlight {
  color: #ff4d29;
}

.services-details p {
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 20px;
  font-family: "Sora", sans-serif;
  font-weight: 500;
}

/* Scroll-triggered animation for paragraph */
.services-section.scroll-animate .services-details p {
  animation: fadeInUp 0.8s ease-out 1.4s both;
}

.services-details ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

/* Scroll-triggered animation for list */
.services-section.scroll-animate .services-details ul {
  animation: fadeInUp 0.8s ease-out 1.6s both;
}

.services-details li {
  margin-bottom: 15px;
  font-family: "Sora", sans-serif;
  font-size: 1rem;
  color: #555;
  line-height: 1.6;
  position: relative;
  padding-left: 20px;
  transition: all 0.3s ease;
}

/* Staggered animations for list items */
.services-section.scroll-animate .services-details li:nth-child(1) {
  animation: fadeInUp 0.8s ease-out 1.8s both;
}

.services-section.scroll-animate .services-details li:nth-child(2) {
  animation: fadeInUp 0.8s ease-out 2s both;
}

.services-section.scroll-animate .services-details li:nth-child(3) {
  animation: fadeInUp 0.8s ease-out 2.2s both;
}

.services-section.scroll-animate .services-details li:nth-child(4) {
  animation: fadeInUp 0.8s ease-out 2.4s both;
}

.services-details li:hover {
  transform: translateX(5px);
  color: #333;
}

.services-details li::before {
  content: "•";
  color: #ff4d29;
  font-size: 1.5rem;
  position: absolute;
  left: 0;
  top: -2px;
}

.services-details li strong {
  color: #333;
  font-weight: 600;
}

.sand-casting-section {
  margin-top: 50px;
  padding: 0 40px;
  text-align: center;
}

/* Scroll-triggered animation for sand casting sections */
.sand-casting-section.scroll-animate {
  animation: fadeInUp 1.2s ease-out 0.3s forwards;
}

.sand-casting-title {
  font-family: "Montserrat", sans-serif;
  font-size: 1.5rem;
  font-weight: 1000;
  color: #333;
  margin-bottom: 30px;
  text-align: center;
}

/* Scroll-triggered animation for titles */
.sand-casting-section.scroll-animate .sand-casting-title {
  animation: fadeInUp 0.8s ease-out 0.6s both;
}

.sand-casting-row {
  display: flex;
  justify-content: center;
  gap: 60px;
  flex-wrap: wrap;
  max-width: 1000px;
  margin: 0 auto;
}

/* Scroll-triggered animation for casting rows */
.sand-casting-section.scroll-animate .sand-casting-row {
  animation: fadeInUp 0.8s ease-out 0.8s both;
}

.sand-casting-item {
  position: relative;
  width: 280px;
  height: 250px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.4s ease;
}

/* Staggered animations for casting items */
.sand-casting-section.scroll-animate .sand-casting-item:nth-child(1) {
  animation: scaleIn 0.8s ease-out 1s both;
}

.sand-casting-section.scroll-animate .sand-casting-item:nth-child(2) {
  animation: scaleIn 0.8s ease-out 1.2s both;
}

.sand-casting-section.scroll-animate .sand-casting-item:nth-child(3) {
  animation: scaleIn 0.8s ease-out 1.4s both;
}

.sand-casting-section.scroll-animate .sand-casting-item:nth-child(n + 4) {
  animation: scaleIn 0.8s ease-out 1.6s both;
}

.sand-casting-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.sand-casting-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.sand-casting-item:hover .sand-casting-img {
  transform: scale(1.05);
}

.sand-casting-label {
  position: absolute;
  bottom: 0;
  right: 0;
  left: auto;
  width: 50%;
  background: rgb(255 39 10 / 50%);
  color: white;
  font-family: "Montserrat", sans-serif;
  font-size: 1rem;
  font-weight: 700;
  text-align: center;
  padding: 10px 8px;
  letter-spacing: 0.5px;
}

@media (max-width: 1200px) {
  .services-section {
    max-width: 95vw;
    padding: 10px 30px 0 30px;
    gap: 40px;
  }

  .services-header-flex-row {
    padding: 10px 30px 0 30px;
    gap: 30px;
  }

  .services-search {
    width: 500px;
    max-width: 500px;
  }
}

@media (max-width: 1100px) {
  .services-section {
    flex-direction: column;
    padding: 30px 20px;
    gap: 30px;
    align-items: center;
  }

  .services-sidebar {
    width: 100%;
    max-width: 600px;
    min-width: auto;
    padding-right: 0;
    text-align: center;
  }

  .services-sidebar::after {
    display: none;
  }

  .services-content {
    padding: 30px 20px;
    max-width: 100%;
  }

  .services-header-flex-row {
    max-width: 100%;
    padding: 10px 20px 0 20px;
  }

  .services-search {
    width: 100%;
    max-width: 500px;
  }

  .sand-casting-section {
    padding: 0 20px;
  }

  .sand-casting-row {
    gap: 40px;
  }
}

@media (max-width: 768px) {
  .services-section {
    padding: 20px 15px;
    gap: 20px;
  }

  .services-content {
    padding: 20px 15px;
  }

  .services-header-flex-row {
    padding: 10px 15px 0 15px;
  }

  .services-details h2 {
    font-size: 2rem;
    text-align: center;
  }

  .services-search {
    width: 100%;
    max-width: 100%;
    padding: 12px 15px 12px 45px;
  }

  .search-icon {
    left: 15px;
    font-size: 16px;
  }

  .sand-casting-section {
    margin-top: 30px;
    padding: 0 15px;
  }

  .sand-casting-title {
    font-size: 1.2rem;
    margin-bottom: 20px;
  }

  .sand-casting-row {
    justify-content: center;
    gap: 20px;
  }

  .sand-casting-item {
    width: 140px;
    height: 110px;
  }

  .sand-casting-label {
    font-size: 0.8rem;
    padding: 6px 4px;
  }
}

@media (max-width: 700px) {
  .services-title-main {
    font-size: 1.3rem;
  }

  .services-header-flex-row {
    padding: 20px 15px 0 15px;
  }
}

@media (max-width: 480px) {
  .services-section {
    padding: 15px 10px;
  }

  .services-header-flex-row {
    padding: 15px 10px 0 10px;
  }

  .services-title-main {
    font-size: 1.1rem;
  }

  .services-search {
    padding: 10px 12px 10px 40px;
    font-size: 0.9rem;
  }

  .search-icon {
    left: 12px;
    font-size: 14px;
  }

  .sand-casting-section {
    padding: 0 10px;
  }

  .sand-casting-row {
    gap: 15px;
  }

  .sand-casting-item {
    width: 120px;
    height: 90px;
  }

  .sand-casting-label {
    font-size: 0.7rem;
    padding: 4px 2px;
  }
}

@media (max-width: 900px) {
  .services-header-flex-row {
    flex-direction: column;
    align-items: center;
    gap: 20px;
    padding: 24px 20px 0 20px;
  }

  .services-title-main {
    font-size: 1.5rem;
    text-align: center;
  }

  .search-container {
    width: 100%;
    display: flex;
    justify-content: center;
  }
}

.our-products-section {
  margin: 60px auto 0 auto;
  max-width: 1200px;
  padding-bottom: 40px;
  position: relative;
  text-align: center;
}

/* Scroll-triggered animation for products section */
.our-products-section.scroll-animate {
  animation: fadeInUp 1.2s ease-out 0.3s forwards;
}

.our-products-images-row {
  display: flex;
  justify-content: center;
  gap: 60px;
  margin-bottom: -40px;
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;
}

/* Scroll-triggered animation for images row */
.our-products-section.scroll-animate .our-products-images-row {
  animation: fadeInUp 0.8s ease-out 0.6s both;
}

.our-product-img {
  width: calc(33.33% - 14px);
  height: 240px;
  object-fit: cover;
  border-radius: 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.4s ease;
}

/* Staggered animations for product images */
.our-products-section.scroll-animate .our-product-img:nth-child(1) {
  animation: scaleIn 0.8s ease-out 0.8s both;
}

.our-products-section.scroll-animate .our-product-img:nth-child(2) {
  animation: scaleIn 0.8s ease-out 1s both;
}

.our-products-section.scroll-animate .our-product-img:nth-child(3) {
  animation: scaleIn 0.8s ease-out 1.2s both;
}

.our-product-img:hover {
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.our-products-banner {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0;
  position: relative;
  z-index: 1;
  width: 100%;
}

/* Scroll-triggered animation for banner */
.our-products-section.scroll-animate .our-products-banner {
  animation: fadeInUp 0.8s ease-out 1.4s both;
}

.our-products-banner span {
  background: #ff270a;
  color: #fff;
  font-size: 2.5rem;
  font-weight: 800;
  padding: 20px 190px;
  border-radius: 0;
  letter-spacing: 1px;
  font-family: "Montserrat", sans-serif;
  display: inline-block;
  width: auto;
  text-align: center;
  max-width: 800px;
  margin-top: 25px;
}

.our-products-list {
  display: flex;
  flex-direction: column;
  gap: 36px;
}

.product-category {
  margin-bottom: 0;
}
.product-category-title {
  font-size: 1.15rem;
  font-weight: 700;
  color: #222;
  margin-bottom: 12px;
  margin-left: 6px;
  font-family: "Montserrat", sans-serif;
}
.product-category-row {
  display: flex;
  gap: 32px;
  margin-bottom: 0;
}
.product-img-label {
  position: relative;
  width: 170px;
  height: 140px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.product-img-label img {
  width: 100%;
  height: 100px;
  object-fit: cover;
  border-radius: 5px;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.06);
}
.product-label {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 32px;
  background: rgba(255, 39, 10, 0.75);
  color: #fff;
  font-size: 1rem;
  font-weight: 700;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0 0 5px 5px;
  font-family: "Montserrat", sans-serif;
  letter-spacing: 0.5px;
}

@media (max-width: 900px) {
  .our-products-section {
    max-width: 98vw;
    padding: 0 10px 30px 10px;
    margin-top: 40px;
  }

  .our-products-images-row {
    gap: 0;
    margin-bottom: -25px;
  }

  .our-product-img {
    height: 120px;
  }

  .our-products-banner span {
    font-size: 1.5rem;
    padding: 15px 40px;
  }

  .product-category-row {
    gap: 10px;
  }
  .product-img-label {
    width: 90px;
    height: 70px;
  }
  .product-img-label img {
    height: 50px;
  }
  .product-label {
    font-size: 0.8rem;
    height: 18px;
    padding: 2px 0;
  }
}
