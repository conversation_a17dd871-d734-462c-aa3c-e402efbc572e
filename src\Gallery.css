/* ===============================================
   ANIMATION KEYFRAMES AND UTILITIES
   =============================================== */

/* Smooth scrolling for the entire page */
html {
  scroll-behavior: smooth;
}

/* Global animation keyframes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-40px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scaleX(0);
  }
  to {
    opacity: 1;
    transform: scaleX(1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Performance optimizations for animations */
*,
*::before,
*::after {
  backface-visibility: hidden;
  transform-style: preserve-3d;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Page load animation */
.gallery-page {
  opacity: 0;
  animation: fadeIn 0.8s ease-out 0.2s forwards;
}

/* Gallery.css */

.gallery-section {
  padding: 80px 40px;
  background-color: #f7f7f7;
  text-align: left;
}

/* Scroll-triggered animation for gallery section */
.gallery-section.scroll-animate {
  animation: fadeInUp 1.2s ease-out 0.3s forwards;
}

.gallery-header {
  margin-bottom: 50px;
}

/* Scroll-triggered animation for header */
.gallery-section.scroll-animate .gallery-header {
  animation: fadeInUp 0.8s ease-out 0.6s both;
}

.gallery-heading {
  font-family: "Montserrat", sans-serif;
  font-size: 3rem;
  font-weight: 800;
  color: #000;
  margin-bottom: 10px;
  position: relative;
  display: inline-block;
}

/* Scroll-triggered animation for heading */
.gallery-section.scroll-animate .gallery-heading {
  animation: fadeInLeft 0.8s ease-out 0.8s both;
}

.gallery-heading::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: -5px;
  width: 120px;
  height: 3px;
  background-color: #ff270a;
}

/* Scroll-triggered animation for underline */
.gallery-section.scroll-animate .gallery-heading::after {
  animation: slideInScale 0.8s ease-out 1s both;
}

.gallery-subheading {
  font-family: "Montserrat", sans-serif;
  font-size: 1.2rem;
  font-weight: 600;
  color: #ff270a;
  margin-bottom: 10px;
}

/* Scroll-triggered animation for subheading */
.gallery-section.scroll-animate .gallery-subheading {
  animation: fadeInUp 0.8s ease-out 1.2s both;
}

.gallery-description {
  font-family: "Sora", sans-serif;
  font-size: 1rem;
  color: #535353;
  max-width: 800px;
  margin: 0;
  line-height: 1.6;
}

/* Scroll-triggered animation for description */
.gallery-section.scroll-animate .gallery-description {
  animation: fadeInUp 0.8s ease-out 1.4s both;
}

.gallery-main-image-container {
  margin: 50px auto;
  max-width: 90%;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.4s ease;
}

/* Scroll-triggered animation for image container */
.gallery-main-image-container.scroll-animate {
  animation: scaleIn 1.2s ease-out 0.6s both;
}

.gallery-main-image-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.gallery-main-image {
  width: 100%;
  height: auto;
  object-fit: contain;
  display: block;
  margin: 0 auto;
  transition: transform 0.4s ease;
}

.gallery-main-image-container:hover .gallery-main-image {
  transform: scale(1.05);
}

@media (max-width: 900px) {
  .gallery-section {
    padding: 60px 30px;
  }

  .gallery-heading {
    font-size: 2.5rem;
  }

  .gallery-main-image-container {
    margin: 30px auto;
  }
}

@media (max-width: 600px) {
  .gallery-section {
    padding: 40px 20px;
  }

  .gallery-heading {
    font-size: 2rem;
  }

  .gallery-subheading {
    font-size: 1rem;
  }

  .gallery-description {
    font-size: 0.9rem;
  }

  .gallery-main-image-container {
    margin: 30px auto;
  }
}
