/* ===============================================
   ANIMATION KEYFRAMES AND UTILITIES
   =============================================== */

/* Smooth scrolling for the entire page */
html {
  scroll-behavior: smooth;
}

/* Global animation keyframes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-40px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(40px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scaleX(0);
  }
  to {
    opacity: 1;
    transform: scaleX(1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInBottom {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Performance optimizations for animations */
*,
*::before,
*::after {
  backface-visibility: hidden;
  transform-style: preserve-3d;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* ===============================================
   COMPREHENSIVE MOBILE RESPONSIVE STYLES
   =============================================== */

/* Global responsive fixes */
.about-us-page-content {
  width: 100%;
  min-height: 100vh;
  padding: 0;
  background-color: #ffffff;
  overflow-x: hidden;
  opacity: 0;
  animation: fadeIn 0.8s ease-out 0.2s forwards;
}

/* Ensure images don't exceed container width */
img {
  max-width: 100%;
  height: auto;
}

/* Fix any potential text overflow */
* {
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Who We Are Section Responsive Design */
@media (max-width: 1200px) {
  .about-who-we-are-section {
    height: 80vh;
  }

  .about-who-we-are-content {
    padding: 0 60px;
    max-width: 550px;
  }

  .about-who-we-are-heading {
    font-size: 2.5rem;
  }

  .about-who-we-are-subheading {
    font-size: 1.4rem;
  }
}

@media (max-width: 992px) {
  .about-who-we-are-section {
    height: 70vh;
    min-height: 400px;
  }

  .about-who-we-are-content {
    padding: 0 40px;
    max-width: 500px;
  }

  .about-who-we-are-heading {
    font-size: 2.3rem;
  }

  .about-who-we-are-subheading {
    font-size: 1.3rem;
  }

  .about-who-we-are-text {
    font-size: 1.05rem;
  }
}

@media (max-width: 768px) {
  .about-who-we-are-section {
    height: 60vh;
    min-height: 350px;
  }

  .about-who-we-are-content {
    padding: 0 20px;
    max-width: 90%;
  }

  .about-who-we-are-heading {
    font-size: 2rem;
    margin-bottom: 15px;
  }

  .about-who-we-are-subheading {
    font-size: 1.2rem;
    margin-bottom: 15px;
  }

  .about-who-we-are-text {
    font-size: 1rem;
  }

  .about-who-we-are-underline {
    width: 100px;
    margin-bottom: 15px;
  }
}

@media (max-width: 600px) {
  .about-who-we-are-section {
    height: 50vh;
    min-height: 300px;
  }

  .about-who-we-are-content {
    padding: 0 15px;
  }

  .about-who-we-are-heading {
    font-size: 1.7rem;
  }

  .about-who-we-are-subheading {
    font-size: 1.1rem;
  }

  .about-who-we-are-text {
    font-size: 0.95rem;
  }
}

@media (max-width: 480px) {
  .about-who-we-are-section {
    height: 45vh;
    min-height: 280px;
  }

  .about-who-we-are-content {
    padding: 0 12px;
  }

  .about-who-we-are-heading {
    font-size: 1.5rem;
  }

  .about-who-we-are-subheading {
    font-size: 1rem;
  }

  .about-who-we-are-text {
    font-size: 0.9rem;
  }

  .about-who-we-are-underline {
    width: 80px;
    height: 3px;
  }
}

@media (max-width: 375px) {
  .about-who-we-are-section {
    height: 40vh;
    min-height: 220px;
  }

  .about-who-we-are-content {
    padding: 0 8px;
    max-width: 80%;
  }

  .about-who-we-are-heading {
    font-size: 1.3rem;
    margin-bottom: 6px;
    line-height: 1.2;
  }

  .about-who-we-are-subheading {
    font-size: 0.85rem;
    margin-bottom: 8px;
  }

  .about-who-we-are-text {
    font-size: 0.8rem;
    line-height: 1.2;
  }

  .about-who-we-are-underline {
    width: 70px;
    height: 2px;
    margin-bottom: 8px;
  }
}

@media (max-width: 320px) {
  .about-who-we-are-section {
    height: 35vh;
    min-height: 200px;
  }

  .about-who-we-are-content {
    padding: 0 6px;
    max-width: 75%;
  }

  .about-who-we-are-heading {
    font-size: 1.2rem;
    margin-bottom: 5px;
  }

  .about-who-we-are-subheading {
    font-size: 0.8rem;
    margin-bottom: 6px;
  }

  .about-who-we-are-text {
    font-size: 0.75rem;
    line-height: 1.1;
  }

  .about-who-we-are-underline {
    width: 60px;
    height: 2px;
    margin-bottom: 6px;
  }
}

/* Founder's Story Section Responsive Design */
@media (max-width: 1200px) {
  .founder-story-section {
    padding: 60px 30px;
    gap: 60px;
  }

  .founder-story-heading {
    font-size: 2.5rem;
  }

  .founder-story-image-container {
    width: 400px;
    height: 500px;
  }
}

@media (max-width: 992px) {
  .founder-story-section {
    flex-direction: column;
    text-align: center;
    gap: 50px;
    padding: 50px 20px;
  }

  .founder-story-content {
    max-width: 100%;
    order: 2;
  }

  .founder-story-image-container {
    order: 1;
    width: 350px;
    height: 420px;
    margin: 0 auto;
  }

  .founder-story-heading {
    font-size: 2.2rem;
  }

  .founder-story-label::before {
    font-size: 2.5rem;
    top: -20px;
  }
}

@media (max-width: 768px) {
  .founder-story-section {
    padding: 40px 15px;
    gap: 40px;
  }

  .founder-story-heading {
    font-size: 1.9rem;
  }

  .founder-story-text {
    font-size: 1rem;
  }

  .founder-story-image-container {
    width: 300px;
    height: 360px;
  }

  .founder-story-red-corner-top-right,
  .founder-story-red-corner-bottom-left {
    width: 80px;
    height: 80px;
  }

  .founder-story-red-corner-top-right .horizontal-bar,
  .founder-story-red-corner-top-right .vertical-bar,
  .founder-story-red-corner-bottom-left .horizontal-bar,
  .founder-story-red-corner-bottom-left .vertical-bar {
    height: 18px;
    width: 18px;
  }
}

@media (max-width: 600px) {
  .founder-story-section {
    padding: 30px 12px;
    gap: 30px;
  }

  .founder-story-heading {
    font-size: 1.7rem;
  }

  .founder-story-text {
    font-size: 0.95rem;
  }

  .founder-story-image-container {
    width: 280px;
    height: 330px;
  }

  .founder-story-label::before {
    font-size: 2rem;
    top: -15px;
  }

  .founder-story-red-corner-top-right,
  .founder-story-red-corner-bottom-left {
    width: 70px;
    height: 70px;
  }

  .founder-story-red-corner-top-right .horizontal-bar,
  .founder-story-red-corner-top-right .vertical-bar,
  .founder-story-red-corner-bottom-left .horizontal-bar,
  .founder-story-red-corner-bottom-left .vertical-bar {
    height: 15px;
    width: 15px;
  }
}

@media (max-width: 480px) {
  .founder-story-section {
    padding: 25px 10px;
  }

  .founder-story-heading {
    font-size: 1.5rem;
  }

  .founder-story-text {
    font-size: 0.9rem;
  }

  .founder-story-image-container {
    width: 250px;
    height: 300px;
  }

  .founder-story-red-corner-top-right,
  .founder-story-red-corner-bottom-left {
    width: 60px;
    height: 60px;
  }

  .founder-story-red-corner-top-right .horizontal-bar,
  .founder-story-red-corner-top-right .vertical-bar,
  .founder-story-red-corner-bottom-left .horizontal-bar,
  .founder-story-red-corner-bottom-left .vertical-bar {
    height: 12px;
    width: 12px;
  }
}

@media (max-width: 375px) {
  .founder-story-section {
    padding: 25px 8px;
    gap: 20px;
  }

  .founder-story-heading {
    font-size: 1.4rem;
    margin-bottom: 8px;
  }

  .founder-story-text {
    font-size: 0.85rem;
    line-height: 1.2;
  }

  .founder-story-image-container {
    width: 220px;
    height: 270px;
  }

  .founder-story-red-corner-top-right,
  .founder-story-red-corner-bottom-left {
    width: 60px;
    height: 60px;
  }
  .founder-story-red-corner-top-right .horizontal-bar,
  .founder-story-red-corner-top-right .vertical-bar,
  .founder-story-red-corner-bottom-left .horizontal-bar,
  .founder-story-red-corner-bottom-left .vertical-bar {
    height: 10px;
    width: 10px;
  }

  .founder-story-label::before {
    font-size: 1.8rem;
    top: -12px;
  }
}

@media (max-width: 320px) {
  .founder-story-section {
    padding: 20px 6px;
    gap: 15px;
  }

  .founder-story-heading {
    font-size: 1.2rem;
    margin-bottom: 6px;
  }

  .founder-story-text {
    font-size: 0.8rem;
    line-height: 1.1;
  }

  .founder-story-image-container {
    width: 200px;
    height: 250px;
  }

  .founder-story-red-corner-top-right,
  .founder-story-red-corner-bottom-left {
    width: 50px;
    height: 50px;
  }
  .founder-story-red-corner-top-right .horizontal-bar,
  .founder-story-red-corner-top-right .vertical-bar,
  .founder-story-red-corner-bottom-left .horizontal-bar,
  .founder-story-red-corner-bottom-left .vertical-bar {
    height: 8px;
    width: 8px;
  }

  .founder-story-label::before {
    font-size: 1.6rem;
    top: -10px;
  }
}

/* Vision and Mission Sections Responsive Design */
@media (max-width: 1200px) {
  .our-vision-section,
  .our-mission-section {
    padding: 60px 30px;
    gap: 60px;
  }

  .our-vision-heading,
  .our-mission-heading {
    font-size: 1.7rem;
  }

  .our-vision-image-container,
  .our-mission-image-container {
    max-width: 450px;
    height: 320px;
  }
}

@media (max-width: 992px) {
  .our-vision-section,
  .our-mission-section {
    flex-direction: column;
    text-align: center;
    gap: 40px;
    padding: 50px 20px;
  }

  .our-vision-content,
  .our-mission-content {
    max-width: 100%;
    order: 2;
  }

  .our-vision-image-container,
  .our-mission-image-container {
    order: 1;
    max-width: 100%;
    height: 300px;
  }

  .our-vision-heading,
  .our-mission-heading {
    font-size: 2.2rem;
  }

  .our-vision-text,
  .our-mission-list li {
    font-size: 1rem;
  }

  .our-vision-label::before,
  .our-mission-label::before {
    font-size: 2.5rem;
    top: -20px;
  }
}

@media (max-width: 768px) {
  .our-vision-section,
  .our-mission-section {
    padding: 40px 15px;
    gap: 30px;
  }

  .our-vision-heading,
  .our-mission-heading {
    font-size: 1.8rem;
  }

  .our-vision-text,
  .our-mission-list li {
    font-size: 0.95rem;
  }

  .our-vision-image-container,
  .our-mission-image-container {
    height: 250px;
  }
}

@media (max-width: 600px) {
  .our-vision-section,
  .our-mission-section {
    padding: 30px 12px;
  }

  .our-vision-heading,
  .our-mission-heading {
    font-size: 1.6rem;
  }

  .our-vision-text,
  .our-mission-list li {
    font-size: 0.9rem;
  }

  .our-vision-image-container,
  .our-mission-image-container {
    height: 220px;
  }

  .our-vision-label::before,
  .our-mission_label::before {
    font-size: 2rem;
    top: -15px;
  }
}

@media (max-width: 480px) {
  .our-vision-section,
  .our-mission-section {
    padding: 25px 10px;
  }

  .our-vision-heading,
  .our-mission-heading {
    font-size: 1.4rem;
  }

  .our-vision-text,
  .our-mission-list li {
    font-size: 0.85rem;
  }

  .our-vision-image-container,
  .our-mission-image-container {
    height: 200px;
  }

  .our-vision-label::before,
  .our-mission_label::before {
    font-size: 1.8rem;
    top: -12px;
  }
}

/* Achievements Section Responsive Design */
@media (max-width: 1200px) {
  .achievements-section {
    padding: 60px 30px;
  }

  .achievements-heading {
    font-size: 1.4rem;
  }

  .achievements-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
  }
}

@media (max-width: 992px) {
  .achievements-section {
    padding: 50px 20px;
  }

  .achievements-heading {
    font-size: 1.8rem;
  }

  .achievements-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
  }

  .achievement-card {
    padding: 25px 20px;
  }

  .achievement-icon-container {
    width: 70px;
    height: 70px;
  }

  .achievement-icon {
    font-size: 30px;
  }

  .achievement-title {
    font-size: 1.2rem;
  }

  .achievement-description {
    font-size: 0.95rem;
  }
}

@media (max-width: 768px) {
  .achievements-section {
    padding: 40px 15px;
  }

  .achievements-heading {
    font-size: 1.6rem;
  }

  .achievements-grid {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 20px;
  }

  .achievement-card {
    padding: 20px 15px;
  }

  .achievement-icon-container {
    width: 65px;
    height: 65px;
  }

  .achievement-icon {
    font-size: 28px;
  }

  .achievement-title {
    font-size: 1.1rem;
  }

  .achievement-description {
    font-size: 0.9rem;
  }
}

@media (max-width: 600px) {
  .achievements-section {
    padding: 30px 12px;
  }

  .achievements-heading {
    font-size: 1.4rem;
    line-height: 1.3;
  }

  .achievements-grid {
    grid-template-columns: 1fr;
    gap: 18px;
  }

  .achievement-card {
    padding: 18px 12px;
  }

  .achievement-icon-container {
    width: 60px;
    height: 60px;
  }

  .achievement-icon {
    font-size: 26px;
  }

  .achievement-title {
    font-size: 1rem;
  }

  .achievement-description {
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .achievements-section {
    padding: 25px 10px;
  }

  .achievements-heading {
    font-size: 1.2rem;
  }

  .achievement-card {
    padding: 15px 10px;
  }

  .achievement-icon-container {
    width: 55px;
    height: 55px;
    margin-bottom: 15px;
  }

  .achievement-icon-container::before {
    width: 45px;
    height: 45px;
  }

  .achievement-icon {
    font-size: 24px;
  }

  .achievement-title {
    font-size: 0.95rem;
    margin-bottom: 8px;
  }

  .achievement-description {
    font-size: 0.8rem;
  }
}

/* Legacy styles fixes */
@media (max-width: 768px) {
  .welcome-to-gait-432461 {
    font-size: 1.8rem;
  }

  .items-432472 {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .image-44-432457,
  .image-438165,
  .image-438174,
  .image-438199 {
    height: 200px;
    max-width: 100%;
  }
}

@media (max-width: 600px) {
  .welcome-to-gait-432461 {
    font-size: 1.6rem;
  }

  .image-44-432457,
  .image-438165,
  .image-438174,
  .image-438199 {
    height: 180px;
  }
}

@media (max-width: 480px) {
  .welcome-to-gait-432461 {
    font-size: 1.4rem;
  }

  .image-44-432457,
  .image-438165,
  .image-438174,
  .image-438199 {
    height: 160px;
  }
}

/* Very small screens support */
@media (max-width: 320px) {
  .about-who-we-are-section {
    height: 40vh;
    min-height: 250px;
  }

  .about-who-we-are-content {
    padding: 0 8px;
  }

  .about-who-we-are-heading {
    font-size: 1.3rem;
  }

  .about-who-we-are-subheading {
    font-size: 0.9rem;
  }

  .about-who-we-are-text {
    font-size: 0.8rem;
  }

  .founder-story-heading {
    font-size: 1.3rem;
  }

  .founder-story-image-container {
    width: 220px;
    height: 270px;
  }

  .our-vision-heading,
  .our-mission-heading {
    font-size: 1.2rem;
  }

  .achievements-heading {
    font-size: 1rem;
  }

  .achievement-icon-container {
    width: 50px;
    height: 50px;
  }

  .achievement-icon {
    font-size: 20px;
  }

  .achievement-title {
    font-size: 0.9rem;
  }

  .achievement-description {
    font-size: 0.75rem;
  }
}

.welcome-to-gait-432461 {
  font-size: 2.5rem;
  font-weight: bold;
  text-align: center;
}

.welcome-to-gait-432461-1 {
  color: #007bff;
}

.image-44-432457 {
  width: 100%;
  max-width: 800px;
  height: 700px;
  margin: 2rem 0;
}

.image-44-432457 img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.frame-36-432478,
.frame-35-432483 {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  margin: 1rem 0;
}

.call-us-432481,
.mail-us-432486 {
  font-weight: bold;
  font-size: 1.2rem;
}

.items-432472 {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin: 2rem 0;
}

.items-432472 > div {
  cursor: pointer;
  font-weight: 500;
}

.who-we-are-432492,
.founders-story-432454,
.our-vision-432526,
.our-mission-438202 {
  font-size: 2rem;
  font-weight: bold;
  text-align: center;
  margin: 2rem 0;
}

.driven-by-exper-438164,
.engineering-exc-432550,
.shaping-the-fut-438177 {
  font-size: 1.5rem;
  font-weight: 600;
  text-align: center;
  margin: 1rem 0;
}

.at-gait-enginee-432493,
.rc-ramesh-babu--432530,
.to-be-a-leading-438178 {
  max-width: 800px;
  margin: 1rem auto;
  line-height: 1.6;
  text-align: center;
}

.image-438165,
.image-438174,
.image-438199 {
  width: 100%;
  max-width: 600px;
  height: 300px;
  margin: 2rem auto;
}

.image-438165 img,
.image-438174 img,
.image-438199 img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.line-3-432488,
.line-4-432489,
.line-6-432491,
.line-8-438162 {
  width: 100%;
  max-width: 200px;
  height: 2px;
  background-color: #007bff;
  margin: 1rem auto;
}

.rectangle-14147-438159,
.rectangle-14148-438160,
.rectangle-14156-438172,
.rectangle-14157-438173 {
  width: 100%;
  max-width: 800px;
  height: 2px;
  background-color: #e0e0e0;
  margin: 1rem auto;
}

@media (max-width: 768px) {
  .welcome-to-gait-432461 {
    font-size: 2rem;
  }

  .items-432472 {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .image-44-432457,
  .image-438165,
  .image-438174,
  .image-438199 {
    height: 200px;
  }
}

.about-who-we-are-section {
  position: relative;
  width: 100%;
  height: 90vh;
  min-height: 500px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  overflow: hidden;
  color: #fff;
}

/* Scroll-triggered animation for who we are section */
.about-who-we-are-section.scroll-animate {
  animation: fadeIn 1.2s ease-out forwards;
}

.about-who-we-are-image-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.about-who-we-are-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  /* Background image animation */
  transition: transform 0.6s ease;
}

.about-who-we-are-section.scroll-animate .about-who-we-are-image {
  animation: scaleIn 2s ease-out forwards;
}

.about-who-we-are-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    rgba(0, 0, 0, 0.75) 0%,
    rgba(0, 0, 0, 0.3) 50%,
    rgba(0, 0, 0, 0.1) 100%
  );
  z-index: 1;
  /* Overlay animation */
  opacity: 0;
}

.about-who-we-are-section.scroll-animate .about-who-we-are-image-overlay {
  animation: fadeIn 1.5s ease-out 0.3s forwards;
}

.about-who-we-are-content {
  position: relative;
  z-index: 2;
  padding: 0 80px;
  max-width: 700px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
}

/* Scroll-triggered animation for content */
.about-who-we-are-section.scroll-animate .about-who-we-are-content {
  animation: fadeInLeft 1.2s ease-out 0.5s both;
}

.about-who-we-are-heading {
  font-family: "Montserrat", sans-serif;
  font-size: 2.8rem;
  font-weight: 800;
  color: #fff;
  margin-bottom: 15px;
  line-height: 1.1;
}

/* Scroll-triggered animation for heading */
.about-who-we-are-section.scroll-animate .about-who-we-are-heading {
  animation: fadeInUp 0.8s ease-out 0.7s both;
}

.about-who-we-are-underline {
  width: 140px;
  height: 4px;
  background-color: #ff270a;
  margin-bottom: 25px;
}

/* Scroll-triggered animation for underline */
.about-who-we-are-section.scroll-animate .about-who-we-are-underline {
  animation: slideInScale 0.8s ease-out 0.9s both;
}

.about-who-we-are-subheading {
  font-family: "Sora", sans-serif;
  font-size: 1.5rem;
  font-weight: 600;
  color: #fff;
  margin-bottom: 25px;
  line-height: 1.3;
}

/* Scroll-triggered animation for subheading */
.about-who-we-are-section.scroll-animate .about-who-we-are-subheading {
  animation: fadeInUp 0.8s ease-out 1.1s both;
}

.about-who-we-are-text {
  font-family: "Sora", sans-serif;
  font-size: 1.1rem;
  font-weight: 400;
  color: #fff;
  line-height: 1.6;
  margin: 0;
}

/* Scroll-triggered animation for text */
.about-who-we-are-section.scroll-animate .about-who-we-are-text {
  animation: fadeInUp 0.8s ease-out 1.3s both;
}

@media (max-width: 900px) {
  .about-who-we-are-section {
    height: 60vh;
    min-height: 350px;
  }

  .about-who-we-are-content {
    padding: 0 20px;
    max-width: 100%;
  }

  .about-who-we-are-heading {
    font-size: 2.2rem;
  }

  .about-who-we-are-subheading {
    font-size: 1.2rem;
  }

  .about-who-we-are-text {
    font-size: 1rem;
  }
}

@media (max-width: 768px) {
  .about-who-we-are-section {
    height: 55vh;
    min-height: 320px;
  }

  .about-who-we-are-content {
    padding: 0 15px;
    max-width: 95%;
  }

  .about-who-we-are-heading {
    font-size: 2rem;
    margin-bottom: 12px;
  }

  .about-who-we-are-subheading {
    font-size: 1.1rem;
    margin-bottom: 15px;
  }

  .about-who-we-are-text {
    font-size: 0.95rem;
    line-height: 1.5;
  }

  .about-who-we-are-underline {
    width: 120px;
    margin-bottom: 15px;
  }
}

@media (max-width: 600px) {
  .about-who-we-are-section {
    height: 50vh;
    min-height: 280px;
  }

  .about-who-we-are-content {
    padding: 0 12px;
    max-width: 90%;
  }

  .about-who-we-are-heading {
    font-size: 1.7rem;
    margin-bottom: 10px;
  }

  .about-who-we-are-subheading {
    font-size: 1rem;
    margin-bottom: 12px;
  }

  .about-who-we-are-text {
    font-size: 0.9rem;
    line-height: 1.4;
  }

  .about-who-we-are-underline {
    width: 100px;
    height: 3px;
    margin-bottom: 12px;
  }
}

@media (max-width: 480px) {
  .about-who-we-are-section {
    height: 45vh;
    min-height: 250px;
  }

  .about-who-we-are-content {
    padding: 0 10px;
    max-width: 85%;
  }

  .about-who-we-are-heading {
    font-size: 1.5rem;
    margin-bottom: 8px;
  }

  .about-who-we-are-subheading {
    font-size: 0.9rem;
    margin-bottom: 10px;
  }

  .about-who-we-are-text {
    font-size: 0.85rem;
    line-height: 1.3;
  }

  .about-who-we-are-underline {
    width: 80px;
    height: 3px;
    margin-bottom: 10px;
  }
}

/* Founder's Story Section Styles */
.founder-story-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 80px; /* Adjust gap as needed */
  padding: 80px 40px;
  background-color: #f7f7f7; /* Light background color */
  position: relative;
  overflow: hidden;
  min-height: 600px;
}

/* Scroll-triggered animation for founder section */
.founder-story-section.scroll-animate {
  animation: fadeInUp 1.2s ease-out 0.3s forwards;
}

.founder-story-content {
  flex: 1;
  max-width: 800px;
  position: relative; /* For watermark positioning */
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* Scroll-triggered animation for content */
.founder-story-section.scroll-animate .founder-story-content {
  animation: fadeInLeft 1.2s ease-out 0.6s both;
}

.founder-story-label {
  font-family: "Montserrat", sans-serif;
  font-size: 1.1rem;
  font-weight: 700;
  color: #ff270a;
  margin-bottom: 10px;
  position: relative;
  z-index: 1;
}

/* Scroll-triggered animation for label */
.founder-story-section.scroll-animate .founder-story-label {
  animation: fadeInUp 0.8s ease-out 0.8s both;
}

.founder-story-label::before {
  content: "Founder's Story"; /* Watermark text */
  position: absolute;
  top: -30px;
  left: 0;
  font-size: 3.5rem; /* Larger for watermark */
  font-weight: 800;
  color: #000;
  opacity: 0.08; /* Semi-transparent */
  z-index: 0;
  white-space: nowrap;
}

.founder-story-heading {
  font-family: "Montserrat", sans-serif;
  font-size: 2.8rem;
  font-weight: 800;
  color: #000;
  margin-bottom: 15px;
  line-height: 1.2;
}

/* Scroll-triggered animation for heading */
.founder-story-section.scroll-animate .founder-story-heading {
  animation: fadeInUp 0.8s ease-out 1s both;
}

.founder-story-underline {
  width: 80px;
  height: 4px;
  background-color: #ff270a;
  margin-bottom: 25px;
}

/* Scroll-triggered animation for underline */
.founder-story-section.scroll-animate .founder-story-underline {
  animation: slideInScale 0.8s ease-out 1.2s both;
}

.founder-story-text {
  font-family: "Sora", sans-serif;
  font-size: 1.1rem;
  font-weight: 400;
  color: #535353;
  line-height: 1.6;
  margin: 0;
}

/* Scroll-triggered animation for text */
.founder-story-section.scroll-animate .founder-story-text {
  animation: fadeInUp 0.8s ease-out 1.4s both;
}

.founder-story-image-container {
  position: relative;
  width: 450px; /* Increased width */
  height: 550px; /* Increased height */
  overflow: hidden;
  flex-shrink: 0;
  /* Hover effects */
  transition: all 0.4s ease;
}

/* Scroll-triggered animation for image container */
.founder-story-section.scroll-animate .founder-story-image-container {
  animation: fadeInRight 1.2s ease-out 0.8s both;
}

.founder-story-image-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.founder-story-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 2; /* Ensure image is above red corners */
  /* Image animation */
  transition: transform 0.4s ease;
}

.founder-story-image-container:hover .founder-story-image {
  transform: scale(1.05);
}

.founder-story-red-corner-top-right,
.founder-story-red-corner-bottom-left {
  position: absolute;
  z-index: 1; /* Ensure red corners are below the image */
}

.founder-story-red-corner-top-right {
  top: 0;
  right: 0;
  width: 120px; /* Overall width of the L-shape container */
  height: 120px; /* Overall height of the L-shape container */
}

.founder-story-red-corner-bottom-left {
  bottom: 0;
  left: 0;
  width: 120px; /* Overall width of the L-shape container */
  height: 120px; /* Overall height of the L-shape container */
}

.founder-story-red-corner-top-right .horizontal-bar {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 25px; /* Thickness of the bar */
  background-color: #ff270a;
}

.founder-story-red-corner-top-right .vertical-bar {
  position: absolute;
  top: 0;
  right: 0;
  width: 25px; /* Thickness of the bar */
  height: 100%;
  background-color: #ff270a;
}

.founder-story-red-corner-bottom-left .horizontal-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 25px; /* Thickness of the bar */
  background-color: #ff270a;
}

.founder-story-red-corner-bottom-left .vertical-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 25px; /* Thickness of the bar */
  height: 100%;
  background-color: #ff270a;
}

@media (max-width: 900px) {
  .founder-story-section {
    flex-direction: column; /* Stack content on smaller screens */
    text-align: center;
    gap: 50px; /* Adjusted gap for smaller screens */
    padding: 60px 30px;
  }

  .founder-story-content {
    max-width: 100%;
    order: 2;
  }

  .founder-story-image-container {
    order: 1;
    width: 350px; /* Adjusted for smaller screens */
    height: 420px; /* Adjusted for smaller screens */
    margin: 0 auto;
  }

  .founder-story-label::before {
    font-size: 2.5rem;
    top: -20px;
  }

  .founder-story-heading {
    font-size: 2.2rem;
  }

  .founder-story-red-corner-top-right,
  .founder-story-red-corner-bottom-left {
    width: 100px; /* Adjusted for smaller screens */
    height: 100px; /* Adjusted for smaller screens */
  }
  .founder-story-red-corner-top-right .horizontal-bar,
  .founder-story-red-corner-top-right .vertical-bar,
  .founder-story-red-corner-bottom-left .horizontal-bar,
  .founder-story-red-corner-bottom-left .vertical-bar {
    height: 20px; /* Adjusted thickness */
    width: 20px; /* Adjusted thickness */
  }
}

@media (max-width: 768px) {
  .founder-story-section {
    padding: 50px 20px;
    gap: 40px;
  }

  .founder-story-heading {
    font-size: 2rem;
    margin-bottom: 15px;
  }

  .founder-story-text {
    font-size: 1rem;
    line-height: 1.5;
  }

  .founder-story-image-container {
    width: 320px;
    height: 380px;
  }

  .founder-story-red-corner-top-right,
  .founder-story-red-corner-bottom-left {
    width: 90px;
    height: 90px;
  }
  .founder-story-red-corner-top-right .horizontal-bar,
  .founder-story-red-corner-top-right .vertical-bar,
  .founder-story-red-corner-bottom-left .horizontal-bar,
  .founder-story-red-corner-bottom-left .vertical-bar {
    height: 18px;
    width: 18px;
  }
}

@media (max-width: 600px) {
  .founder-story-section {
    padding: 40px 15px;
    gap: 30px;
  }

  .founder-story-label::before {
    font-size: 2rem;
    top: -15px;
  }

  .founder-story-heading {
    font-size: 1.8rem;
    margin-bottom: 12px;
  }

  .founder-story-text {
    font-size: 0.95rem;
    line-height: 1.4;
  }

  .founder-story-image-container {
    width: 280px; /* Adjusted for smaller screens */
    height: 340px; /* Adjusted for smaller screens */
  }

  .founder-story-red-corner-top-right,
  .founder-story-red-corner-bottom-left {
    width: 80px; /* Adjusted for smaller screens */
    height: 80px; /* Adjusted for smaller screens */
  }
  .founder-story-red-corner-top-right .horizontal-bar,
  .founder-story-red-corner-top-right .vertical-bar,
  .founder-story-red-corner-bottom-left .horizontal-bar,
  .founder-story-red-corner-bottom-left .vertical-bar {
    height: 15px; /* Adjusted thickness */
    width: 15px; /* Adjusted thickness */
  }
}

@media (max-width: 480px) {
  .founder-story-section {
    padding: 30px 12px;
    gap: 25px;
  }

  .founder-story-heading {
    font-size: 1.6rem;
    margin-bottom: 10px;
  }

  .founder-story-text {
    font-size: 0.9rem;
    line-height: 1.3;
  }

  .founder-story-image-container {
    width: 250px;
    height: 300px;
  }

  .founder-story-red-corner-top-right,
  .founder-story-red-corner-bottom-left {
    width: 70px;
    height: 70px;
  }
  .founder-story-red-corner-top-right .horizontal-bar,
  .founder-story-red-corner-top-right .vertical-bar,
  .founder-story-red-corner-bottom-left .horizontal-bar,
  .founder-story-red-corner-bottom-left .vertical-bar {
    height: 12px;
    width: 12px;
  }
}

@media (max-width: 375px) {
  .founder-story-section {
    padding: 25px 8px;
    gap: 20px;
  }

  .founder-story-heading {
    font-size: 1.4rem;
    margin-bottom: 8px;
  }

  .founder-story-text {
    font-size: 0.85rem;
    line-height: 1.2;
  }

  .founder-story-image-container {
    width: 220px;
    height: 270px;
  }

  .founder-story-red-corner-top-right,
  .founder-story-red-corner-bottom-left {
    width: 60px;
    height: 60px;
  }
  .founder-story-red-corner-top-right .horizontal-bar,
  .founder-story-red-corner-top-right .vertical-bar,
  .founder-story-red-corner-bottom-left .horizontal-bar,
  .founder-story-red-corner-bottom-left .vertical-bar {
    height: 10px;
    width: 10px;
  }

  .founder-story-label::before {
    font-size: 1.8rem;
    top: -12px;
  }
}

@media (max-width: 320px) {
  .founder-story-section {
    padding: 20px 6px;
    gap: 15px;
  }

  .founder-story-heading {
    font-size: 1.2rem;
    margin-bottom: 6px;
  }

  .founder-story-text {
    font-size: 0.8rem;
    line-height: 1.1;
  }

  .founder-story-image-container {
    width: 200px;
    height: 250px;
  }

  .founder-story-red-corner-top-right,
  .founder-story-red-corner-bottom-left {
    width: 50px;
    height: 50px;
  }
  .founder-story-red-corner-top-right .horizontal-bar,
  .founder-story-red-corner-top-right .vertical-bar,
  .founder-story-red-corner-bottom-left .horizontal-bar,
  .founder-story-red-corner-bottom-left .vertical-bar {
    height: 8px;
    width: 8px;
  }

  .founder-story-label::before {
    font-size: 1.6rem;
    top: -10px;
  }
}

/* Our Vision Section Styles */
.our-vision-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 80px;
  padding: 80px 40px;
  background-color: #ffffff; /* White background */
  position: relative;
  overflow: hidden;
}

/* Scroll-triggered animation for vision section */
.our-vision-section.scroll-animate {
  animation: fadeInUp 1.2s ease-out 0.3s forwards;
}

.our-vision-image-container {
  flex: 1;
  max-width: 500px; /* Adjust as needed */
  height: 350px; /* Adjust as needed */
  overflow: hidden;
  /* Hover effects */
  transition: all 0.4s ease;
}

/* Scroll-triggered animation for image container */
.our-vision-section.scroll-animate .our-vision-image-container {
  animation: fadeInLeft 1.2s ease-out 0.6s both;
}

.our-vision-image-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.our-vision-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  /* Image animation */
  transition: transform 0.4s ease;
}

.our-vision-image-container:hover .our-vision-image {
  transform: scale(1.05);
}

.our-vision-content {
  flex: 1;
  max-width: 600px;
  position: relative; /* For watermark positioning */
}

/* Scroll-triggered animation for content */
.our-vision-section.scroll-animate .our-vision-content {
  animation: fadeInRight 1.2s ease-out 0.8s both;
}

.our-vision-label {
  font-family: "Montserrat", sans-serif;
  font-size: 1.5rem;
  font-weight: 700;
  color: #ff270a;
  margin-bottom: 5px;
  position: relative;
  z-index: 1;
}

/* Scroll-triggered animation for label */
.our-vision-section.scroll-animate .our-vision-label {
  animation: fadeInUp 0.8s ease-out 1s both;
}

.our-vision-label::before {
  content: "Vision"; /* Watermark text */
  position: absolute;
  top: -30px;
  left: 0;
  font-size: 3.5rem; /* Larger for watermark */
  font-weight: 800;
  color: #000;
  opacity: 0.08; /* Semi-transparent */
  z-index: 0;
  white-space: nowrap;
}

.our-vision-heading {
  font-family: "Montserrat", sans-serif;
  font-size: 1.8rem;
  font-weight: 800;
  color: #000;
  margin-bottom: 20px;
  line-height: 1.2;
}

/* Scroll-triggered animation for heading */
.our-vision-section.scroll-animate .our-vision-heading {
  animation: fadeInUp 0.8s ease-out 1.2s both;
}

.our-vision-text {
  font-family: "Sora", sans-serif;
  font-size: 1.1rem;
  font-weight: 400;
  color: #535353;
  line-height: 1.6;
}

/* Scroll-triggered animation for text */
.our-vision-section.scroll-animate .our-vision-text {
  animation: fadeInUp 0.8s ease-out 1.4s both;
}

/* Our Mission Section Styles */
.our-mission-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 80px;
  padding: 80px 40px;
  background-color: #f7f7f7; /* Light background color */
  position: relative;
  overflow: hidden;
}

/* Scroll-triggered animation for mission section */
.our-mission-section.scroll-animate {
  animation: fadeInUp 1.2s ease-out 0.3s forwards;
}

.our-mission-content {
  flex: 1;
  max-width: 600px;
  position: relative; /* For watermark positioning */
}

/* Scroll-triggered animation for content */
.our-mission-section.scroll-animate .our-mission-content {
  animation: fadeInLeft 1.2s ease-out 0.6s both;
}

.our-mission-label {
  font-family: "Montserrat", sans-serif;
  font-size: 1.5rem;
  font-weight: 700;
  color: #ff270a;
  margin-bottom: 5px;
  position: relative;
  z-index: 1;
}

/* Scroll-triggered animation for label */
.our-mission-section.scroll-animate .our-mission-label {
  animation: fadeInUp 0.8s ease-out 0.8s both;
}

.our-mission-label::before {
  content: "Mission"; /* Watermark text */
  position: absolute;
  top: -30px;
  left: 0;
  font-size: 3.5rem; /* Larger for watermark */
  font-weight: 800;
  color: #000;
  opacity: 0.08; /* Semi-transparent */
  z-index: 0;
  white-space: nowrap;
}

.our-mission-heading {
  font-family: "Montserrat", sans-serif;
  font-size: 1.8rem;
  font-weight: 800;
  color: #000;
  margin-bottom: 20px;
  line-height: 1.2;
}

/* Scroll-triggered animation for heading */
.our-mission-section.scroll-animate .our-mission-heading {
  animation: fadeInUp 0.8s ease-out 1s both;
}

.our-mission-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.our-mission-list li {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15px;
  font-family: "Sora", sans-serif;
  font-size: 1.1rem;
  font-weight: 400;
  color: #535353;
  line-height: 1.6;
}

/* Scroll-triggered animation for list items */
.our-mission-section.scroll-animate .our-mission-list li:nth-child(1) {
  animation: fadeInUp 0.8s ease-out 1.2s both;
}

.our-mission-section.scroll-animate .our-mission-list li:nth-child(2) {
  animation: fadeInUp 0.8s ease-out 1.4s both;
}

.our-mission-section.scroll-animate .our-mission-list li:nth-child(3) {
  animation: fadeInUp 0.8s ease-out 1.6s both;
}

.our-mission-section.scroll-animate .our-mission-list li:nth-child(4) {
  animation: fadeInUp 0.8s ease-out 1.8s both;
}

.our-mission-section.scroll-animate .our-mission-list li:nth-child(n + 5) {
  animation: fadeInUp 0.8s ease-out 2s both;
}

.our-mission-list .bullet-icon {
  display: inline-block;
  width: 18px;
  height: 18px;
  min-width: 18px; /* Ensure it doesn't shrink */
  margin-right: 10px;
  background-color: #ff270a; /* Red bullet color */
  border-radius: 50%;
  position: relative;
  top: 4px; /* Adjust to align with text */
}

.our-mission-image-container {
  flex: 1;
  max-width: 500px; /* Adjust as needed */
  height: 350px; /* Adjust as needed */
  overflow: hidden;
  /* Hover effects */
  transition: all 0.4s ease;
}

/* Scroll-triggered animation for image container */
.our-mission-section.scroll-animate .our-mission-image-container {
  animation: fadeInRight 1.2s ease-out 0.8s both;
}

.our-mission-image-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.our-mission-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  /* Image animation */
  transition: transform 0.4s ease;
}

.our-mission-image-container:hover .our-mission-image {
  transform: scale(1.05);
}

@media (max-width: 900px) {
  .our-vision-section,
  .our-mission-section {
    flex-direction: column; /* Stack content on smaller screens */
    text-align: center;
    gap: 40px;
  }

  .our-vision-image-container,
  .our-mission-image-container {
    max-width: 100%;
    height: 300px;
  }

  .our-vision-content,
  .our-mission-content {
    max-width: 100%;
  }

  .our-vision_label::before,
  .our-mission_label::before {
    font-size: 2.5rem;
    top: -20px;
  }

  .our-vision-heading,
  .our-mission-heading {
    font-size: 2.2rem;
  }

  .our-vision-text,
  .our-mission-list li {
    font-size: 1rem;
  }
}

@media (max-width: 600px) {
  .our-vision-section,
  .our-mission-section {
    padding: 40px 20px;
  }

  .our-vision-label::before,
  .our-mission-label::before {
    font-size: 2rem;
  }

  .our-vision-heading,
  .our-mission-heading {
    font-size: 1.8rem;
  }

  .our-vision-image-container,
  .our-mission-image-container {
    height: 250px;
  }

  .our-mission-list li {
    font-size: 0.9rem;
  }
}

/* Achievements Section Styles */
.achievements-section {
  padding: 80px 40px;
  background-color: #ffffff;
  text-align: center;
}

/* Scroll-triggered animation for achievements section */
.achievements-section.scroll-animate {
  animation: fadeInUp 1.2s ease-out 0.3s forwards;
}

.achievements-header {
  margin-bottom: 50px;
}

/* Scroll-triggered animation for header */
.achievements-section.scroll-animate .achievements-header {
  animation: fadeInUp 0.8s ease-out 0.6s both;
}

.achievements-label {
  font-family: "Montserrat", sans-serif;
  font-size: 1.5rem;
  font-weight: 700;
  color: #ff270a;
  margin-bottom: 10px;
  position: relative;
  display: inline-block;
}

/* Scroll-triggered animation for label */
.achievements-section.scroll-animate .achievements-label {
  animation: fadeInUp 0.8s ease-out 0.8s both;
}

.achievements-label::after {
  content: "";
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: -5px;
  width: 50px;
  height: 3px;
  background-color: #ff270a;
}

.achievements-heading {
  font-family: "Montserrat", sans-serif;
  font-size: 1.5rem;
  font-weight: 400;
  color: #000;
  line-height: 1.4;
  max-width: 900px;
  margin: 0 auto;
}

/* Scroll-triggered animation for heading */
.achievements-section.scroll-animate .achievements-heading {
  animation: fadeInUp 0.8s ease-out 1s both;
}

.achievements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
  max-width: 1200px;
  margin: 0 auto;
}

/* Scroll-triggered animation for grid */
.achievements-section.scroll-animate .achievements-grid {
  animation: fadeInUp 0.8s ease-out 1.2s both;
}

.achievement-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 30px;
  /* background-color: #f7f7f7; */
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease;
}

/* Staggered animations for achievement cards */
.achievements-section.scroll-animate .achievement-card:nth-child(1) {
  animation: scaleIn 0.8s ease-out 1.4s both;
}

.achievements-section.scroll-animate .achievement-card:nth-child(2) {
  animation: scaleIn 0.8s ease-out 1.6s both;
}

.achievements-section.scroll-animate .achievement-card:nth-child(3) {
  animation: scaleIn 0.8s ease-out 1.8s both;
}

.achievements-section.scroll-animate .achievement-card:nth-child(n + 4) {
  animation: scaleIn 0.8s ease-out 2s both;
}

.achievement-card:hover {
  transform: translateY(-5px);
}

.achievement-icon-container {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 2px solid #ff270a;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  background-color: #fff;
  position: relative;
  box-shadow: 0 0 0 5px rgba(255, 39, 10, 0.1);
  transition: all 0.4s ease;
}

.achievement-icon-container:hover {
  transform: scale(1.1);
  box-shadow: 0 0 0 10px rgba(255, 39, 10, 0.15);
}

.achievement-icon-container::before {
  content: "";
  position: absolute;
  width: 100px; /* Increased width */
  height: 100px; /* Increased height */
  border-radius: 50%;
  border: 1px solid rgba(255, 39, 10, 0.3);
  z-index: -1;
  transition: all 0.4s ease;
}

.achievement-icon-container:hover::before {
  width: 110px;
  height: 110px;
  border-color: rgba(255, 39, 10, 0.5);
}

.achievement-icon {
  font-size: 36px;
  color: #ff270a;
  transition: all 0.3s ease;
}

.achievement-icon-container:hover .achievement-icon {
  transform: scale(1.1);
}

.achievement-title {
  font-family: "Montserrat", sans-serif;
  font-size: 1.4rem;
  font-weight: 700;
  color: #ed2424;
  margin-bottom: 10px;
}

.achievement-description {
  font-family: "Sora", sans-serif;
  font-size: 1rem;
  font-weight: 400;
  color: #535353;
  line-height: 1.6;
}

@media (max-width: 900px) {
  .achievements-heading {
    font-size: 2rem;
  }

  .achievements-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
  }

  .achievement-card {
    padding: 25px;
  }

  .achievement-icon-container {
    width: 70px;
    height: 70px;
  }

  .achievement-icon-container::before {
    width: 60px;
    height: 60px;
  }

  .achievement-icon {
    font-size: 30px;
  }

  .achievement-title {
    font-size: 1.2rem;
  }

  .achievement-description {
    font-size: 0.95rem;
  }
}

@media (max-width: 600px) {
  .achievements-section {
    padding: 40px 20px;
  }

  .achievements-heading {
    font-size: 1.8rem;
  }

  .achievements-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .achievement-card {
    padding: 20px;
  }

  .achievement-icon-container {
    width: 60px;
    height: 60px;
  }

  .achievement-icon-container::before {
    width: 50px;
    height: 50px;
  }

  .achievement-icon {
    font-size: 26px;
  }

  .achievement-title {
    font-size: 1.1rem;
  }

  .achievement-description {
    font-size: 0.9rem;
  }
}
