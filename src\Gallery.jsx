import React, { useEffect } from "react";
import Navbar from "./components/Navbar";
import Footer from "./components/Footer";
import TopBar from "./components/TopBar";
import "./Gallery.css";
import galleryImg from "./images/gallery-img.jpg";

const Gallery = () => {
  // Scroll-triggered animation effect
  useEffect(() => {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: "0px 0px -50px 0px",
    };

    const observer = new IntersectionObserver(entries => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add("scroll-animate");
        }
      });
    }, observerOptions);

    // Observe all sections that should have scroll animations
    const sectionsToAnimate = document.querySelectorAll(
      ".gallery-section, .gallery-header, .gallery-main-image-container"
    );

    sectionsToAnimate.forEach(section => {
      observer.observe(section);
    });

    // Cleanup observer on component unmount
    return () => {
      sectionsToAnimate.forEach(section => {
        observer.unobserve(section);
      });
    };
  }, []);

  return (
    <div className="gallery-page">
      <TopBar />
      <Navbar />
      <div className="gallery-section">
        <div className="gallery-header">
          <h1 className="gallery-heading">Our Gallery</h1>
          <p className="gallery-subheading">
            Crafting Excellence, One Cast at a Time.
          </p>
          <p className="gallery-description">
            Explore our precision-engineered die-casting solutions in action.
          </p>
        </div>
        <div className="gallery-main-image-container">
          <img
            src={galleryImg}
            alt="Main Gallery"
            className="gallery-main-image"
          />
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default Gallery;
